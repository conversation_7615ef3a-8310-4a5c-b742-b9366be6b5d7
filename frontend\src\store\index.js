import { createStore } from "vuex";
import user from "./modules/user";
import seat from "./modules/seat";

/**
 * Vuex状态管理
 * 管理应用的全局状态，包括用户信息、座位信息等
 */

export default createStore({
  state: {
    appName: "基于国密算法的图书馆自习室座位管理系统",
    appVersion: "1.0.0",
    loading: false,
    error: null,
  },
  getters: {
    appName: (state) => state.appName,
    appVersion: (state) => state.appVersion,
    isLoading: (state) => state.loading,
    error: (state) => state.error,
  },
  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    SET_ERROR(state, error) {
      state.error = error;
    },
    CLEAR_ERROR(state) {
      state.error = null;
    },
  },
  actions: {
    setLoading({ commit }, loading) {
      commit("SET_LOADING", loading);
    },
    setError({ commit }, error) {
      commit("SET_ERROR", error);
    },
    clearError({ commit }) {
      commit("CLEAR_ERROR");
    },
  },
  modules: {
    user,
    seat,
  },
});
