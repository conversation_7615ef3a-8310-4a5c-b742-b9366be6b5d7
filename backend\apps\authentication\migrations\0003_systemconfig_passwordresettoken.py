# Generated by Django 5.2.1 on 2025-06-09 13:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0002_user_is_active_user_is_staff_user_is_superuser_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='配置键')),
                ('value', models.TextField(verbose_name='配置值')),
                ('config_type', models.CharField(choices=[('encryption', '加密配置'), ('email', '邮件配置'), ('security', '安全配置'), ('system', '系统配置')], max_length=20, verbose_name='配置类型')),
                ('description', models.TextField(blank=True, verbose_name='配置描述')),
                ('is_encrypted', models.BooleanField(default=False, verbose_name='是否加密存储')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '系统配置',
                'verbose_name_plural': '系统配置',
                'db_table': 'auth_system_config',
                'ordering': ['config_type', 'key'],
            },
        ),
        migrations.CreateModel(
            name='PasswordResetToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=64, unique=True, verbose_name='重置令牌')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('is_used', models.BooleanField(default=False, verbose_name='是否已使用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('used_at', models.DateTimeField(blank=True, null=True, verbose_name='使用时间')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.user', verbose_name='用户')),
            ],
            options={
                'verbose_name': '密码重置令牌',
                'verbose_name_plural': '密码重置令牌',
                'db_table': 'auth_password_reset_token',
                'ordering': ['-created_at'],
            },
        ),
    ]
