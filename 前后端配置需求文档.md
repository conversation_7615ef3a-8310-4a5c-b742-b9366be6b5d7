# 基于国密算法的图书馆自习室座位管理系统 - 前后端配置需求文档

## 项目概述

本项目是一个基于国密算法（SM2/SM3/SM4）的图书馆自习室座位管理系统，采用前后端分离架构，后端使用Django框架，前端使用Vue.js 3框架。

## 系统架构

- **后端**: Django + MySQL + Celery
- **前端**: Vue.js 3 + Element Plus + Vuex
- **安全**: 国密SM2/SM3/SM4算法
- **部署**: WSGI/ASGI应用服务器

## 后端配置需求

### 1. 环境要求

#### Python环境
- **Python版本**: 3.8+
- **包管理器**: pip
- **虚拟环境**: 推荐使用venv或conda

#### 数据库要求
- **数据库**: MySQL 5.7+ / 8.0+
- **字符集**: utf8mb4
- **连接器**: PyMySQL

### 2. 核心依赖包

```txt
# Web框架
Django==5.2.1
djangorestframework==3.14.0
djangorestframework_simplejwt==5.5.0

# 数据库
PyMySQL==1.0.2

# 异步支持
channels==4.2.2
asgiref==3.8.1

# 任务队列
celery==5.5.3
django-celery-beat==2.8.1
kombu==5.5.4
billiard==4.2.1

# 国密算法
gmssl==3.2.2
cryptography==45.0.3
pycryptodomex==3.22.0

# 跨域支持
django-cors-headers==3.13.0

# 其他工具
pillow==11.2.1
qrcode==8.2
python-dateutil==2.9.0.post0
pytz==2025.2
```

### 3. 环境变量配置

创建 `.env` 文件或设置系统环境变量：

```bash
# Django基础配置
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# 数据库配置
DB_NAME=seat_management
DB_USER=root
DB_PASSWORD=your-mysql-password
DB_HOST=localhost
DB_PORT=3306

# 国密算法配置
SYSTEM_MASTER_KEY=your-32-character-master-key-here

# 前端URL配置
FRONTEND_URL=http://localhost:8080

# 邮件配置
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>
```

### 4. 数据库配置

#### MySQL配置示例
```sql
-- 创建数据库
CREATE DATABASE seat_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'seat_user'@'localhost' IDENTIFIED BY 'your-password';
GRANT ALL PRIVILEGES ON seat_management.* TO 'seat_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Django数据库设置
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ.get('DB_NAME', 'seat_management'),
        'USER': os.environ.get('DB_USER', 'root'),
        'PASSWORD': os.environ.get('DB_PASSWORD', '123456'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '3306'),
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

### 5. 缓存配置

```python
# 开发环境 - 本地内存缓存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# 生产环境 - Redis缓存（可选）
# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.redis.RedisCache',
#         'LOCATION': 'redis://127.0.0.1:6379/1',
#     }
# }
```

### 6. Celery配置

```python
# 开发环境 - 内存代理
CELERY_BROKER_URL = 'memory://'
CELERY_RESULT_BACKEND = 'cache'
CELERY_CACHE_BACKEND = 'default'

# 生产环境 - Redis代理（推荐）
# CELERY_BROKER_URL = 'redis://localhost:6379/0'
# CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
```

### 7. 国密算法配置

```python
# SM2密钥路径
SM2_PUBLIC_KEY_PATH = os.path.join(BASE_DIR, 'keys', 'sm2_public_key.pem')
SM2_PRIVATE_KEY_PATH = os.path.join(BASE_DIR, 'keys', 'sm2_private_key.pem')

# 系统主密钥（用于加密配置）
SYSTEM_MASTER_KEY = os.environ.get('SYSTEM_MASTER_KEY', 'default_master_key_32_characters')
```

### 8. 日志配置

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file', 'console'],
        'level': 'INFO',
    },
}
```

## 前端配置需求

### 1. 环境要求

#### Node.js环境
- **Node.js版本**: 14.0+ 或 16.0+
- **包管理器**: npm 或 yarn
- **构建工具**: Vue CLI 5.0+

### 2. 核心依赖包

```json
{
  "dependencies": {
    "vue": "^3.2.13",
    "vue-router": "^4.5.1",
    "vuex": "^4.1.0",
    "element-plus": "^2.9.10",
    "axios": "^1.9.0",
    "sm-crypto": "^0.3.13",
    "qrcodejs2": "^0.0.2",
    "core-js": "^3.8.3"
  },
  "devDependencies": {
    "@vue/cli-service": "~5.0.0",
    "@vue/cli-plugin-babel": "~5.0.0",
    "@vue/cli-plugin-eslint": "~5.0.0",
    "@vue/cli-plugin-router": "~5.0.0",
    "@vue/cli-plugin-vuex": "~5.0.0",
    "eslint": "^7.32.0",
    "eslint-config-prettier": "^8.3.0",
    "eslint-plugin-prettier": "^4.0.0",
    "eslint-plugin-vue": "^8.0.3",
    "prettier": "^2.4.1",
    "sass": "^1.32.7",
    "sass-loader": "^12.0.0"
  }
}
```

### 3. 环境变量配置

创建 `.env` 文件：

```bash
# 开发环境
VUE_APP_API_URL=http://localhost:8000/api/v1
VUE_APP_WS_URL=ws://localhost:8000/ws
VUE_APP_TITLE=基于国密算法的图书馆自习室座位管理系统

# 生产环境 (.env.production)
VUE_APP_API_URL=https://your-domain.com/api/v1
VUE_APP_WS_URL=wss://your-domain.com/ws
VUE_APP_TITLE=基于国密算法的图书馆自习室座位管理系统
```

### 4. Vue CLI配置

```javascript
// vue.config.js
const { defineConfig } = require("@vue/cli-service");

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        ws: true,
      }
    }
  },
  
  // 生产构建配置
  publicPath: process.env.NODE_ENV === 'production' ? '/static/' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  
  // CSS配置
  css: {
    loaderOptions: {
      sass: {
        additionalData: `@import "@/assets/styles/variables.scss";`
      }
    }
  }
});
```

### 5. ESLint和Prettier配置

```javascript
// .eslintrc.js
module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: [
    "plugin:vue/vue3-essential",
    "eslint:recommended",
    "plugin:prettier/recommended",
  ],
  parserOptions: {
    parser: "@babel/eslint-parser",
    requireConfigFile: false,
  },
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
  },
};
```

```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": false,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### 6. TypeScript配置（可选）

```json
// jsconfig.json
{
  "compilerOptions": {
    "target": "es5",
    "module": "esnext",
    "baseUrl": "./",
    "moduleResolution": "node",
    "paths": {
      "@/*": ["src/*"]
    },
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"]
  }
}
```

## 部署配置需求

### 1. 后端部署

#### WSGI服务器配置（Gunicorn）
```bash
# 安装Gunicorn
pip install gunicorn

# 启动命令
gunicorn backend.wsgi:application --bind 0.0.0.0:8000 --workers 4
```

#### ASGI服务器配置（Daphne）
```bash
# 安装Daphne
pip install daphne

# 启动命令
daphne -b 0.0.0.0 -p 8000 backend.asgi:application
```

#### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /static/ {
        alias /path/to/your/project/static/;
    }

    location /media/ {
        alias /path/to/your/project/media/;
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

### 2. 前端部署

#### 构建命令
```bash
# 安装依赖
npm install

# 开发环境启动
npm run serve

# 生产环境构建
npm run build

# 代码检查
npm run lint
```

#### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-frontend-domain.com;
    root /path/to/your/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 3. 系统服务配置

#### Systemd服务配置（Django）
```ini
# /etc/systemd/system/seat-management.service
[Unit]
Description=Seat Management Django Application
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project
Environment=DJANGO_SETTINGS_MODULE=backend.settings
ExecStart=/path/to/venv/bin/gunicorn backend.wsgi:application --bind 127.0.0.1:8000
Restart=always

[Install]
WantedBy=multi-user.target
```

#### Systemd服务配置（Celery）
```ini
# /etc/systemd/system/seat-management-celery.service
[Unit]
Description=Seat Management Celery Worker
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project
Environment=DJANGO_SETTINGS_MODULE=backend.settings
ExecStart=/path/to/venv/bin/celery -A backend worker -l info
Restart=always

[Install]
WantedBy=multi-user.target
```

#### Systemd服务配置（Celery Beat）
```ini
# /etc/systemd/system/seat-management-celerybeat.service
[Unit]
Description=Seat Management Celery Beat
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project
Environment=DJANGO_SETTINGS_MODULE=backend.settings
ExecStart=/path/to/venv/bin/celery -A backend beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
Restart=always

[Install]
WantedBy=multi-user.target
```

## 安全配置需求

### 1. 国密算法配置

#### SM2密钥生成
```python
# 生成SM2密钥对
from gmssl import sm2

# 生成密钥对
private_key, public_key = sm2.generate_keypair()

# 保存密钥到文件
with open('keys/sm2_private_key.pem', 'w') as f:
    f.write(private_key)

with open('keys/sm2_public_key.pem', 'w') as f:
    f.write(public_key)
```

#### SM4密钥配置
```python
# SM4密钥自动生成和管理
from utils.crypto_utils import SM4Crypto

# 系统启动时自动生成SM4密钥
sm4_key = SM4Crypto.generate_key()
```

### 2. HTTPS配置

#### SSL证书配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 其他配置...
}
```

### 3. 防火墙配置

```bash
# UFW防火墙配置示例
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 3306/tcp  # MySQL (仅内网)
sudo ufw enable
```

## 监控和日志配置

### 1. 日志轮转配置

```bash
# /etc/logrotate.d/seat-management
/path/to/your/project/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload seat-management
    endscript
}
```

### 2. 系统监控

#### 健康检查端点
```python
# Django健康检查视图
from django.http import JsonResponse
from django.db import connection

def health_check(request):
    try:
        # 检查数据库连接
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")

        return JsonResponse({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=503)
```

## 备份和恢复配置

### 1. 数据库备份

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"
DB_NAME="seat_management"

# 创建备份
mysqldump -u root -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete
```

### 2. 应用备份

```bash
#!/bin/bash
# app_backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/app_backups"
PROJECT_DIR="/path/to/your/project"

# 备份应用代码和配置
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz \
    --exclude='*.pyc' \
    --exclude='__pycache__' \
    --exclude='node_modules' \
    --exclude='.git' \
    $PROJECT_DIR

# 删除30天前的备份
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +30 -delete
```

## 性能优化配置

### 1. 数据库优化

```sql
-- MySQL配置优化建议
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
max_connections = 200
```

### 2. Django优化

```python
# settings.py 性能优化配置
# 数据库连接池
DATABASES['default']['CONN_MAX_AGE'] = 60

# 会话配置
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# 静态文件压缩
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.ManifestStaticFilesStorage'
```

### 3. 前端优化

```javascript
// vue.config.js 性能优化
module.exports = defineConfig({
  // 代码分割
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      },
    },
  },

  // 压缩配置
  chainWebpack: config => {
    if (process.env.NODE_ENV === 'production') {
      config.plugin('compressionPlugin').use('compression-webpack-plugin', [{
        test: /\.(js|css|html)$/,
        threshold: 10240,
        deleteOriginalAssets: false
      }]);
    }
  }
});
```

## 故障排除

### 1. 常见问题

#### 数据库连接问题
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口占用
netstat -tlnp | grep 3306

# 测试数据库连接
mysql -u root -p -h localhost
```

#### 国密算法问题
```python
# 检查gmssl库安装
python -c "import gmssl; print('gmssl installed successfully')"

# 检查密钥文件权限
ls -la keys/
chmod 600 keys/sm2_private_key.pem
```

#### 前端构建问题
```bash
# 清除缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version
npm --version
```

### 2. 日志分析

```bash
# 查看Django日志
tail -f logs/django.log

# 查看Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 查看系统服务日志
journalctl -u seat-management -f
journalctl -u seat-management-celery -f
```

## 总结

本配置文档涵盖了基于国密算法的图书馆自习室座位管理系统的完整配置需求，包括：

1. **开发环境配置** - Python/Node.js环境、依赖包管理
2. **数据库配置** - MySQL数据库设置和优化
3. **安全配置** - 国密算法配置、HTTPS设置
4. **部署配置** - 生产环境部署方案
5. **监控配置** - 日志管理和系统监控
6. **性能优化** - 数据库和应用性能调优
7. **故障排除** - 常见问题解决方案

请根据实际部署环境调整相关配置参数，确保系统的安全性和稳定性。
