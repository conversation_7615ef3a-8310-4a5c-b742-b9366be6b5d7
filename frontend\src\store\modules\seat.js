import api from "@/api/seat";

const state = {
  rooms: [],
  currentRoom: null,
  seats: [],
  currentSeat: null,
  timeSlots: [],
  myReservations: [],
  reservationHistory: [],
  currentReservation: null,
  activeReservation: null,
  seatStatusMap: new Map(), // 座位状态映射
};

const getters = {
  rooms: (state) => state.rooms,
  currentRoom: (state) => state.currentRoom,
  seats: (state) => state.seats,
  currentSeat: (state) => state.currentSeat,
  timeSlots: (state) => state.timeSlots,
  myReservations: (state) => state.myReservations,
  reservationHistory: (state) => state.reservationHistory,
  currentReservation: (state) => state.currentReservation,
  activeReservation: (state) => state.activeReservation,
  seatStatusMap: (state) => state.seatStatusMap,
};

const mutations = {
  SET_ROOMS(state, rooms) {
    state.rooms = rooms;
  },
  SET_CURRENT_ROOM(state, room) {
    state.currentRoom = room;
  },
  SET_SEATS(state, seats) {
    state.seats = seats;
  },
  SET_CURRENT_SEAT(state, seat) {
    state.currentSeat = seat;
  },
  SET_TIME_SLOTS(state, timeSlots) {
    state.timeSlots = timeSlots;
  },
  SET_MY_RESERVATIONS(state, reservations) {
    state.myReservations = reservations;
  },
  SET_RESERVATION_HISTORY(state, reservations) {
    state.reservationHistory = reservations;
  },
  SET_CURRENT_RESERVATION(state, reservation) {
    state.currentReservation = reservation;
  },
  SET_ACTIVE_RESERVATION(state, reservation) {
    state.activeReservation = reservation;
  },
  UPDATE_RESERVATION_STATUS(state, { reservationId, status }) {
    const index = state.myReservations.findIndex((r) => r.id === reservationId);
    if (index !== -1) {
      state.myReservations[index].status = status;
    }
  },
  UPDATE_SEAT_STATUS(state, seatData) {
    state.seatStatusMap.set(seatData.id, seatData);
    // 更新seats数组中的对应座位
    const seatIndex = state.seats.findIndex((seat) => seat.id === seatData.id);
    if (seatIndex !== -1) {
      state.seats[seatIndex] = { ...state.seats[seatIndex], ...seatData };
    }
  },
};

const actions = {
  // 获取自习室列表
  async getRooms({ commit, dispatch }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getRooms();
      commit("SET_ROOMS", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取自习室列表失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取自习室详情
  async getRoomById({ commit, dispatch }, roomId) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getRoomById(roomId);
      commit("SET_CURRENT_ROOM", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取自习室详情失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取自习室座位列表
  async getSeatsByRoom({ commit, dispatch }, { roomId, date }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getSeatsByRoom(roomId, date);
      commit("SET_SEATS", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取座位列表失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取座位详情
  async getSeatById({ commit, dispatch }, seatId) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getSeatById(seatId);
      commit("SET_CURRENT_SEAT", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取座位详情失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取座位可用时间段
  async getSeatTimeSlots({ commit, dispatch }, { seatId, date }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getSeatTimeSlots(seatId, date);
      commit("SET_TIME_SLOTS", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取座位时间段失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 创建座位预约
  async createReservation(
    { commit, dispatch },
    { seatId, startTime, endTime }
  ) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.createReservation(seatId, startTime, endTime);
      commit("SET_CURRENT_RESERVATION", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "创建预约失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取我的预约列表
  async getMyReservations({ commit, dispatch }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getMyReservations();
      commit("SET_MY_RESERVATIONS", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取我的预约列表失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取预约详情
  async getReservationById({ commit, dispatch }, reservationId) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getReservationById(reservationId);
      commit("SET_CURRENT_RESERVATION", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取预约详情失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取活跃预约
  async getActiveReservation({ commit, dispatch }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getActiveReservation();
      commit("SET_ACTIVE_RESERVATION", response.data);

      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        commit("SET_ACTIVE_RESERVATION", null);
        return null;
      }
      const message = error.response?.data?.message || "获取活跃预约失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 签到
  async checkIn({ commit, dispatch }, requestData) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.checkIn(requestData);

      // 更新活跃预约状态
      if (response.data.reservation) {
        commit("SET_ACTIVE_RESERVATION", response.data.reservation);
      }

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "签到失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 签退
  async checkOut({ commit, dispatch }, requestData) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.checkOut(requestData);

      // 更新活跃预约状态
      if (response.data.reservation) {
        commit("SET_ACTIVE_RESERVATION", response.data.reservation);
      }

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "签退失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 取消预约
  async cancelReservation({ commit, dispatch }, { id }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.cancelReservation(id);

      // 清空活跃预约
      commit("SET_ACTIVE_RESERVATION", null);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "取消预约失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 更新座位状态（WebSocket推送）
  updateSeatStatus({ commit }, seatData) {
    commit("UPDATE_SEAT_STATUS", seatData);
  },

  // 更新预约状态（WebSocket推送）
  updateReservationStatus({ commit, state }, reservationData) {
    // 如果是当前活跃预约，更新活跃预约状态
    if (
      state.activeReservation &&
      state.activeReservation.id === reservationData.id
    ) {
      commit("SET_ACTIVE_RESERVATION", reservationData);
    }

    // 更新我的预约列表中的状态
    commit("UPDATE_RESERVATION_STATUS", {
      reservationId: reservationData.id,
      status: reservationData.status,
    });
  },

  // 获取预约历史记录
  async getReservationHistory({ commit, dispatch }, params = {}) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getReservationHistory(params);
      commit("SET_RESERVATION_HISTORY", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取预约历史记录失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
