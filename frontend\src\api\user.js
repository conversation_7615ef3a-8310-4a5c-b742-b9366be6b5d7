import http from "./http";

/**
 * 用户相关API接口
 * 包括登录、注册、用户信息管理等功能
 */

export default {
  // 用户登录
  login(studentId, password) {
    return http.post("/auth/login", { studentId, password });
  },

  // 获取SM2挑战值
  getSM2Challenge(studentId) {
    return http.get("/auth/sm2-challenge", { params: { studentId } });
  },

  // SM2证书登录
  sm2Login(studentId, signature) {
    return http.post("/auth/sm2-login", { studentId, signature });
  },

  // 用户注册
  register(userData) {
    return http.post("/auth/register", userData);
  },

  // 退出登录
  logout() {
    return http.post("/auth/logout");
  },

  // 获取用户信息
  getUserInfo() {
    return http.get("/user/profile");
  },

  // 更新用户信息
  updateProfile(userData) {
    return http.put("/user/profile", userData);
  },

  // 修改密码
  changePassword(oldPassword, newPassword) {
    return http.put("/user/password", { oldPassword, newPassword });
  },

  // 更新公钥
  updatePublicKey(publicKey) {
    return http.put("/user/public-key", { publicKey });
  },

  // 移除公钥
  removePublicKey() {
    return http.delete("/user/public-key");
  },

  // 获取信誉分记录
  getCreditRecords(params) {
    return http.get("/auth/credit-records", { params });
  },
};
