{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"my-reservations\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  class: \"filter-section\"\n};\nconst _hoisted_5 = {\n  class: \"filter-container\"\n};\nconst _hoisted_6 = {\n  class: \"filter-item\"\n};\nconst _hoisted_7 = {\n  class: \"filter-item\"\n};\nconst _hoisted_8 = {\n  class: \"filter-item\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_10 = {\n  key: 1,\n  class: \"empty-container\"\n};\nconst _hoisted_11 = {\n  key: 2,\n  class: \"reservation-list\"\n};\nconst _hoisted_12 = {\n  class: \"reservation-header\"\n};\nconst _hoisted_13 = {\n  class: \"reservation-status\"\n};\nconst _hoisted_14 = {\n  class: \"reservation-id\"\n};\nconst _hoisted_15 = {\n  class: \"reservation-time\"\n};\nconst _hoisted_16 = {\n  class: \"reservation-content\"\n};\nconst _hoisted_17 = {\n  class: \"seat-info\"\n};\nconst _hoisted_18 = {\n  class: \"seat-location\"\n};\nconst _hoisted_19 = {\n  class: \"seat-number\"\n};\nconst _hoisted_20 = {\n  class: \"time-info\"\n};\nconst _hoisted_21 = {\n  class: \"time-value\"\n};\nconst _hoisted_22 = {\n  class: \"time-duration\"\n};\nconst _hoisted_23 = {\n  class: \"reservation-footer\"\n};\nconst _hoisted_24 = {\n  key: 0,\n  class: \"check-in-dialog\"\n};\nconst _hoisted_25 = {\n  class: \"qr-code-container\"\n};\nconst _hoisted_26 = {\n  class: \"qr-code\",\n  ref: \"qrCodeRef\"\n};\nconst _hoisted_27 = {\n  class: \"check-in-info\"\n};\nconst _hoisted_28 = {\n  class: \"manual-check-in\"\n};\nconst _hoisted_29 = {\n  key: 0,\n  class: \"check-out-dialog\"\n};\nconst _hoisted_30 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_31 = {\n  key: 0,\n  class: \"cancel-dialog\"\n};\nconst _hoisted_32 = {\n  class: \"cancel-warning\"\n};\nconst _hoisted_33 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createElementVNode(\"h2\", null, \"我的预约\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.refreshReservations\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[8] || (_cache[8] = _createTextVNode(\" 刷新 \"))]),\n    _: 1 /* STABLE */,\n    __: [8]\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_card, {\n    shadow: \"never\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"状态：\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.filters.status,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.filters.status = $event),\n      placeholder: \"全部状态\",\n      clearable: \"\",\n      onChange: $setup.handleFilterChange\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.statusOptions, status => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: status.value,\n          label: status.label,\n          value: status.value\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createElementVNode(\"div\", _hoisted_7, [_cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"日期：\", -1 /* HOISTED */)), _createVNode(_component_el_date_picker, {\n      modelValue: $setup.filters.date,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.filters.date = $event),\n      type: \"date\",\n      placeholder: \"选择日期\",\n      format: \"YYYY-MM-DD\",\n      \"value-format\": \"YYYY-MM-DD\",\n      onChange: $setup.handleFilterChange\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createElementVNode(\"div\", _hoisted_8, [_cache[12] || (_cache[12] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"排序：\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.sortBy,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.sortBy = $event),\n      placeholder: \"排序方式\",\n      onChange: $setup.handleSortChange\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.sortOptions, option => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: option.value,\n          label: option.label,\n          value: option.value\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])])])]),\n    _: 1 /* STABLE */\n  })]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_el_skeleton, {\n    rows: 3,\n    animated: \"\"\n  }), _createVNode(_component_el_skeleton, {\n    rows: 3,\n    animated: \"\",\n    style: {\n      \"margin-top\": \"20px\"\n    }\n  })])) : $setup.filteredReservations.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_el_empty, {\n    description: \"没有找到符合条件的预约记录\"\n  })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.filteredReservations, reservation => {\n    return _openBlock(), _createBlock(_component_el_card, {\n      key: reservation.id,\n      class: _normalizeClass([\"reservation-card\", {\n        'reservation-active': $setup.isActiveReservation(reservation)\n      }])\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_tag, {\n        type: $setup.getReservationStatusType(reservation.status)\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getReservationStatusText(reservation.status)), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"]), _createElementVNode(\"span\", _hoisted_14, \"预约号: \" + _toDisplayString(reservation.id), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.formatDate(reservation.created_at)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"h3\", null, _toDisplayString(reservation.seat.room.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_18, _toDisplayString(reservation.seat.room.location), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_19, [_createTextVNode(\" 座位号: \" + _toDisplayString(reservation.seat.seat_number) + \" \", 1 /* TEXT */), reservation.seat.is_power_outlet ? (_openBlock(), _createBlock(_component_el_tag, {\n        key: 0,\n        size: \"small\",\n        effect: \"plain\"\n      }, {\n        default: _withCtx(() => [...(_cache[13] || (_cache[13] = [_createTextVNode(\" 有电源 \")]))]),\n        _: 1 /* STABLE */,\n        __: [13]\n      })) : _createCommentVNode(\"v-if\", true), reservation.seat.is_window_seat ? (_openBlock(), _createBlock(_component_el_tag, {\n        key: 1,\n        size: \"small\",\n        effect: \"plain\"\n      }, {\n        default: _withCtx(() => [...(_cache[14] || (_cache[14] = [_createTextVNode(\" 靠窗 \")]))]),\n        _: 1 /* STABLE */,\n        __: [14]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_20, [_cache[15] || (_cache[15] = _createElementVNode(\"p\", {\n        class: \"time-label\"\n      }, \"预约时间\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_21, _toDisplayString($setup.formatDateTime(reservation.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime(reservation.end_time)), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_22, _toDisplayString($setup.calculateDuration(reservation.start_time, reservation.end_time)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_23, [reservation.status === 'pending' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 0\n      }, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $event => $setup.showCheckInDialog(reservation)\n      }, {\n        default: _withCtx(() => [...(_cache[16] || (_cache[16] = [_createTextVNode(\"签到\")]))]),\n        _: 2 /* DYNAMIC */,\n        __: [16]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n        type: \"danger\",\n        onClick: $event => $setup.showCancelDialog(reservation)\n      }, {\n        default: _withCtx(() => [...(_cache[17] || (_cache[17] = [_createTextVNode(\"取消预约\")]))]),\n        _: 2 /* DYNAMIC */,\n        __: [17]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])], 64 /* STABLE_FRAGMENT */)) : reservation.status === 'checked_in' ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 1,\n        type: \"success\",\n        onClick: $event => $setup.showCheckOutDialog(reservation)\n      }, {\n        default: _withCtx(() => [...(_cache[18] || (_cache[18] = [_createTextVNode(\"签退\")]))]),\n        _: 2 /* DYNAMIC */,\n        __: [18]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        type: \"info\",\n        onClick: $event => $setup.showReservationDetail(reservation)\n      }, {\n        default: _withCtx(() => [...(_cache[19] || (_cache[19] = [_createTextVNode(\"查看详情\")]))]),\n        _: 2 /* DYNAMIC */,\n        __: [19]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]))])]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"class\"]);\n  }), 128 /* KEYED_FRAGMENT */))])), _createCommentVNode(\" 签到对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.checkInDialogVisible,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.checkInDialogVisible = $event),\n    title: \"座位签到\",\n    width: \"400px\"\n  }, {\n    default: _withCtx(() => [$setup.selectedReservation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, null, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"p\", null, \"预约号: \" + _toDisplayString($setup.selectedReservation.id), 1 /* TEXT */), _createElementVNode(\"p\", null, \"座位号: \" + _toDisplayString($setup.selectedReservation.seat?.seat_number), 1 /* TEXT */), _createElementVNode(\"p\", null, \"签到码: \" + _toDisplayString($setup.selectedReservation.reservation_code), 1 /* TEXT */), _cache[20] || (_cache[20] = _createElementVNode(\"p\", {\n      class: \"check-in-tip\"\n    }, \"请使用自习室终端扫描二维码完成签到\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_28, [_createVNode(_component_el_divider, null, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"或手动签到\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.handleCheckIn\n    }, {\n      default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"手动签到\")])),\n      _: 1 /* STABLE */,\n      __: [22]\n    }, 8 /* PROPS */, [\"onClick\"])])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 签退对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.checkOutDialogVisible,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.checkOutDialogVisible = $event),\n    title: \"座位签退\",\n    width: \"400px\"\n  }, {\n    default: _withCtx(() => [$setup.selectedReservation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [_cache[25] || (_cache[25] = _createElementVNode(\"p\", null, \"您确定要签退座位吗？\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, \"座位号: \" + _toDisplayString($setup.selectedReservation.seat?.seat_number), 1 /* TEXT */), _createElementVNode(\"p\", null, \" 预约时间: \" + _toDisplayString($setup.formatDateTime($setup.selectedReservation.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.selectedReservation.end_time)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_30, [_createVNode(_component_el_button, {\n      onClick: _cache[4] || (_cache[4] = $event => $setup.checkOutDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.handleCheckOut,\n      loading: $setup.processing\n    }, {\n      default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\" 确认签退 \")])),\n      _: 1 /* STABLE */,\n      __: [24]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 取消预约对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.cancelDialogVisible,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.cancelDialogVisible = $event),\n    title: \"取消预约\",\n    width: \"400px\"\n  }, {\n    default: _withCtx(() => [$setup.selectedReservation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_cache[28] || (_cache[28] = _createElementVNode(\"p\", null, \"您确定要取消此预约吗？\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, \"座位号: \" + _toDisplayString($setup.selectedReservation.seat?.seat_number), 1 /* TEXT */), _createElementVNode(\"p\", null, \" 预约时间: \" + _toDisplayString($setup.formatDateTime($setup.selectedReservation.start_time)) + \" - \" + _toDisplayString($setup.formatDateTime($setup.selectedReservation.end_time)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_32, [_createVNode(_component_el_alert, {\n      title: \"取消预约提示\",\n      type: \"warning\",\n      description: \"距离预约开始时间不足30分钟取消，可能会影响您的信誉分。\",\n      \"show-icon\": \"\",\n      closable: false\n    })]), _createElementVNode(\"div\", _hoisted_33, [_createVNode(_component_el_button, {\n      onClick: _cache[6] || (_cache[6] = $event => $setup.cancelDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"返回\")])),\n      _: 1 /* STABLE */,\n      __: [26]\n    }), _createVNode(_component_el_button, {\n      type: \"danger\",\n      onClick: $setup.handleCancel,\n      loading: $setup.processing\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"确认取消\")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_button", "type", "onClick", "$setup", "refreshReservations", "default", "_withCtx", "_component_el_icon", "_component_Refresh", "_", "_createTextVNode", "__", "_hoisted_4", "_component_el_card", "shadow", "_hoisted_5", "_hoisted_6", "_component_el_select", "modelValue", "filters", "status", "_cache", "$event", "placeholder", "clearable", "onChange", "handleFilterChange", "_Fragment", "_renderList", "statusOptions", "_createBlock", "_component_el_option", "value", "label", "_hoisted_7", "_component_el_date_picker", "date", "format", "_hoisted_8", "sortBy", "handleSortChange", "sortOptions", "option", "loading", "_hoisted_9", "_component_el_skeleton", "rows", "animated", "style", "filteredReservations", "length", "_hoisted_10", "_component_el_empty", "description", "_hoisted_11", "reservation", "id", "_normalizeClass", "isActiveReservation", "_hoisted_12", "_hoisted_13", "_component_el_tag", "getReservationStatusType", "_toDisplayString", "getReservationStatusText", "_hoisted_14", "_hoisted_15", "formatDate", "created_at", "_hoisted_16", "_hoisted_17", "seat", "room", "name", "_hoisted_18", "location", "_hoisted_19", "seat_number", "is_power_outlet", "size", "effect", "_createCommentVNode", "is_window_seat", "_hoisted_20", "_hoisted_21", "formatDateTime", "start_time", "end_time", "_hoisted_22", "calculateDuration", "_hoisted_23", "showCheckInDialog", "showCancelDialog", "showCheckOutDialog", "showReservationDetail", "_component_el_dialog", "checkInDialogVisible", "title", "width", "selectedReservation", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "reservation_code", "_hoisted_28", "_component_el_divider", "handleCheckIn", "checkOutDialogVisible", "_hoisted_29", "_hoisted_30", "handleCheckOut", "processing", "cancelDialogVisible", "_hoisted_31", "_hoisted_32", "_component_el_alert", "closable", "_hoisted_33", "handleCancel"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\MyReservations.vue"], "sourcesContent": ["<template>\n  <div class=\"my-reservations\">\n    <div class=\"page-header\">\n      <h2>我的预约</h2>\n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"refreshReservations\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n      </div>\n    </div>\n\n    <div class=\"filter-section\">\n      <el-card shadow=\"never\">\n        <div class=\"filter-container\">\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">状态：</span>\n            <el-select\n              v-model=\"filters.status\"\n              placeholder=\"全部状态\"\n              clearable\n              @change=\"handleFilterChange\"\n            >\n              <el-option\n                v-for=\"status in statusOptions\"\n                :key=\"status.value\"\n                :label=\"status.label\"\n                :value=\"status.value\"\n              />\n            </el-select>\n          </div>\n\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">日期：</span>\n            <el-date-picker\n              v-model=\"filters.date\"\n              type=\"date\"\n              placeholder=\"选择日期\"\n              format=\"YYYY-MM-DD\"\n              value-format=\"YYYY-MM-DD\"\n              @change=\"handleFilterChange\"\n            />\n          </div>\n\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">排序：</span>\n            <el-select\n              v-model=\"sortBy\"\n              placeholder=\"排序方式\"\n              @change=\"handleSortChange\"\n            >\n              <el-option\n                v-for=\"option in sortOptions\"\n                :key=\"option.value\"\n                :label=\"option.label\"\n                :value=\"option.value\"\n              />\n            </el-select>\n          </div>\n        </div>\n      </el-card>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"3\" animated />\n      <el-skeleton :rows=\"3\" animated style=\"margin-top: 20px\" />\n    </div>\n\n    <div v-else-if=\"filteredReservations.length === 0\" class=\"empty-container\">\n      <el-empty description=\"没有找到符合条件的预约记录\" />\n    </div>\n\n    <div v-else class=\"reservation-list\">\n      <el-card\n        v-for=\"reservation in filteredReservations\"\n        :key=\"reservation.id\"\n        class=\"reservation-card\"\n        :class=\"{ 'reservation-active': isActiveReservation(reservation) }\"\n      >\n        <div class=\"reservation-header\">\n          <div class=\"reservation-status\">\n            <el-tag :type=\"getReservationStatusType(reservation.status)\">\n              {{ getReservationStatusText(reservation.status) }}\n            </el-tag>\n            <span class=\"reservation-id\">预约号: {{ reservation.id }}</span>\n          </div>\n          <div class=\"reservation-time\">\n            {{ formatDate(reservation.created_at) }}\n          </div>\n        </div>\n\n        <div class=\"reservation-content\">\n          <div class=\"seat-info\">\n            <h3>{{ reservation.seat.room.name }}</h3>\n            <p class=\"seat-location\">{{ reservation.seat.room.location }}</p>\n            <p class=\"seat-number\">\n              座位号: {{ reservation.seat.seat_number }}\n              <el-tag\n                v-if=\"reservation.seat.is_power_outlet\"\n                size=\"small\"\n                effect=\"plain\"\n              >\n                有电源\n              </el-tag>\n              <el-tag\n                v-if=\"reservation.seat.is_window_seat\"\n                size=\"small\"\n                effect=\"plain\"\n              >\n                靠窗\n              </el-tag>\n            </p>\n          </div>\n\n          <div class=\"time-info\">\n            <p class=\"time-label\">预约时间</p>\n            <p class=\"time-value\">\n              {{ formatDateTime(reservation.start_time) }} -\n              {{ formatDateTime(reservation.end_time) }}\n            </p>\n            <p class=\"time-duration\">\n              {{\n                calculateDuration(reservation.start_time, reservation.end_time)\n              }}\n            </p>\n          </div>\n        </div>\n\n        <div class=\"reservation-footer\">\n          <template v-if=\"reservation.status === 'pending'\">\n            <el-button type=\"primary\" @click=\"showCheckInDialog(reservation)\"\n              >签到</el-button\n            >\n            <el-button type=\"danger\" @click=\"showCancelDialog(reservation)\"\n              >取消预约</el-button\n            >\n          </template>\n\n          <template v-else-if=\"reservation.status === 'checked_in'\">\n            <el-button type=\"success\" @click=\"showCheckOutDialog(reservation)\"\n              >签退</el-button\n            >\n          </template>\n\n          <template v-else>\n            <el-button type=\"info\" @click=\"showReservationDetail(reservation)\"\n              >查看详情</el-button\n            >\n          </template>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 签到对话框 -->\n    <el-dialog v-model=\"checkInDialogVisible\" title=\"座位签到\" width=\"400px\">\n      <div v-if=\"selectedReservation\" class=\"check-in-dialog\">\n        <div class=\"qr-code-container\">\n          <div class=\"qr-code\" ref=\"qrCodeRef\"></div>\n        </div>\n\n        <div class=\"check-in-info\">\n          <p>预约号: {{ selectedReservation.id }}</p>\n          <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>\n          <p>签到码: {{ selectedReservation.reservation_code }}</p>\n          <p class=\"check-in-tip\">请使用自习室终端扫描二维码完成签到</p>\n        </div>\n\n        <div class=\"manual-check-in\">\n          <el-divider>或手动签到</el-divider>\n          <el-button type=\"primary\" @click=\"handleCheckIn\">手动签到</el-button>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 签退对话框 -->\n    <el-dialog v-model=\"checkOutDialogVisible\" title=\"座位签退\" width=\"400px\">\n      <div v-if=\"selectedReservation\" class=\"check-out-dialog\">\n        <p>您确定要签退座位吗？</p>\n        <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>\n        <p>\n          预约时间: {{ formatDateTime(selectedReservation.start_time) }} -\n          {{ formatDateTime(selectedReservation.end_time) }}\n        </p>\n\n        <div class=\"dialog-footer\">\n          <el-button @click=\"checkOutDialogVisible = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"handleCheckOut\"\n            :loading=\"processing\"\n          >\n            确认签退\n          </el-button>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 取消预约对话框 -->\n    <el-dialog v-model=\"cancelDialogVisible\" title=\"取消预约\" width=\"400px\">\n      <div v-if=\"selectedReservation\" class=\"cancel-dialog\">\n        <p>您确定要取消此预约吗？</p>\n        <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>\n        <p>\n          预约时间: {{ formatDateTime(selectedReservation.start_time) }} -\n          {{ formatDateTime(selectedReservation.end_time) }}\n        </p>\n\n        <div class=\"cancel-warning\">\n          <el-alert\n            title=\"取消预约提示\"\n            type=\"warning\"\n            description=\"距离预约开始时间不足30分钟取消，可能会影响您的信誉分。\"\n            show-icon\n            :closable=\"false\"\n          />\n        </div>\n\n        <div class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">返回</el-button>\n          <el-button type=\"danger\" @click=\"handleCancel\" :loading=\"processing\"\n            >确认取消</el-button\n          >\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, reactive, nextTick } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { Refresh } from \"@element-plus/icons-vue\";\nimport QRCode from \"qrcodejs2\";\n\nexport default {\n  name: \"MyReservations\",\n  components: {\n    Refresh,\n  },\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const processing = ref(false);\n    const qrCodeRef = ref(null);\n\n    const checkInDialogVisible = ref(false);\n    const checkOutDialogVisible = ref(false);\n    const cancelDialogVisible = ref(false);\n    const selectedReservation = ref(null);\n\n    const filters = reactive({\n      status: \"\",\n      date: \"\",\n    });\n\n    const sortBy = ref(\"start_time\");\n\n    // 状态选项\n    const statusOptions = [\n      { value: \"pending\", label: \"待签到\" },\n      { value: \"checked_in\", label: \"已签到\" },\n      { value: \"completed\", label: \"已完成\" },\n      { value: \"cancelled\", label: \"已取消\" },\n      { value: \"timeout\", label: \"已超时\" },\n    ];\n\n    // 排序选项\n    const sortOptions = [\n      { value: \"start_time\", label: \"按开始时间排序\" },\n      { value: \"created_at\", label: \"按创建时间排序\" },\n      { value: \"status\", label: \"按状态排序\" },\n    ];\n\n    // 获取预约列表\n    const getReservations = async () => {\n      try {\n        loading.value = true;\n        await store.dispatch(\"seat/getMyReservations\");\n      } catch (error) {\n        console.error(\"获取预约列表失败:\", error);\n        ElMessage.error(\"获取预约列表失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新预约列表\n    const refreshReservations = () => {\n      getReservations();\n    };\n\n    // 过滤后的预约列表\n    const filteredReservations = computed(() => {\n      let result = store.getters[\"seat/myReservations\"];\n\n      // 状态过滤\n      if (filters.status) {\n        result = result.filter(\n          (reservation) => reservation.status === filters.status\n        );\n      }\n\n      // 日期过滤\n      if (filters.date) {\n        const filterDate = new Date(filters.date);\n        filterDate.setHours(0, 0, 0, 0);\n\n        const nextDay = new Date(filterDate);\n        nextDay.setDate(nextDay.getDate() + 1);\n\n        result = result.filter((reservation) => {\n          const startTime = new Date(reservation.start_time);\n          return startTime >= filterDate && startTime < nextDay;\n        });\n      }\n\n      // 排序\n      result = [...result].sort((a, b) => {\n        switch (sortBy.value) {\n          case \"created_at\":\n            return new Date(b.created_at) - new Date(a.created_at);\n          case \"status\":\n            return getStatusPriority(a.status) - getStatusPriority(b.status);\n          case \"start_time\":\n          default:\n            return new Date(a.start_time) - new Date(b.start_time);\n        }\n      });\n\n      return result;\n    });\n\n    // 获取状态优先级（用于排序）\n    const getStatusPriority = (status) => {\n      switch (status) {\n        case \"checked_in\":\n          return 1;\n        case \"pending\":\n          return 2;\n        case \"completed\":\n          return 3;\n        case \"cancelled\":\n          return 4;\n        case \"timeout\":\n          return 5;\n        default:\n          return 6;\n      }\n    };\n\n    // 处理过滤变化\n    const handleFilterChange = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 处理排序变化\n    const handleSortChange = () => {\n      // 排序逻辑已在计算属性中实现\n    };\n\n    // 是否是活跃预约（待签到或已签到）\n    const isActiveReservation = (reservation) => {\n      return (\n        reservation.status === \"pending\" || reservation.status === \"checked_in\"\n      );\n    };\n\n    // 显示签到对话框\n    const showCheckInDialog = (reservation) => {\n      selectedReservation.value = reservation;\n      checkInDialogVisible.value = true;\n\n      // 生成二维码\n      nextTick(() => {\n        if (qrCodeRef.value) {\n          // 清空容器\n          qrCodeRef.value.innerHTML = \"\";\n\n          // 生成二维码\n          new QRCode(qrCodeRef.value, {\n            text: reservation.reservation_code,\n            width: 200,\n            height: 200,\n            colorDark: \"#000000\",\n            colorLight: \"#ffffff\",\n            correctLevel: QRCode.CorrectLevel.H,\n          });\n        }\n      });\n    };\n\n    // 显示签退对话框\n    const showCheckOutDialog = (reservation) => {\n      selectedReservation.value = reservation;\n      checkOutDialogVisible.value = true;\n    };\n\n    // 显示取消对话框\n    const showCancelDialog = (reservation) => {\n      selectedReservation.value = reservation;\n      cancelDialogVisible.value = true;\n    };\n\n    // 显示预约详情\n    const showReservationDetail = async (reservation) => {\n      try {\n        // 获取预约详情\n        await store.dispatch(\"seat/getReservationById\", reservation.id);\n\n        // 跳转到预约详情页面\n        router.push({\n          path: `/seat/reservation/${reservation.id}`,\n          query: { mode: \"view\" },\n        });\n      } catch (error) {\n        ElMessage.error(\"获取预约详情失败\");\n      }\n    };\n\n    // 处理签到\n    const handleCheckIn = async () => {\n      if (!selectedReservation.value) return;\n\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/checkIn\", {\n          reservationId: selectedReservation.value.id,\n        });\n\n        ElMessage.success(\"签到成功\");\n        checkInDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理签退\n    const handleCheckOut = async () => {\n      if (!selectedReservation.value) return;\n\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/checkOut\", {\n          reservationId: selectedReservation.value.id,\n        });\n\n        ElMessage.success(\"签退成功\");\n        checkOutDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 处理取消预约\n    const handleCancel = async () => {\n      if (!selectedReservation.value) return;\n\n      try {\n        processing.value = true;\n\n        await store.dispatch(\"seat/cancelReservation\", {\n          reservationId: selectedReservation.value.id,\n        });\n\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        refreshReservations();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        processing.value = false;\n      }\n    };\n\n    // 获取预约状态类型\n    const getReservationStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化日期\n    const formatDate = (dateString) => {\n      if (!dateString) return \"\";\n\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )}`;\n    };\n\n    // 格式化日期时间\n    const formatDateTime = (dateTimeString) => {\n      if (!dateTimeString) return \"\";\n\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 计算时长\n    const calculateDuration = (startTime, endTime) => {\n      if (!startTime || !endTime) return \"\";\n\n      const start = new Date(startTime);\n      const end = new Date(endTime);\n      const diffMs = end - start;\n      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n      return `${diffHrs}小时${diffMins}分钟`;\n    };\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    onMounted(() => {\n      getReservations();\n    });\n\n    return {\n      loading,\n      processing,\n      qrCodeRef,\n      filters,\n      sortBy,\n      statusOptions,\n      sortOptions,\n      checkInDialogVisible,\n      checkOutDialogVisible,\n      cancelDialogVisible,\n      selectedReservation,\n      filteredReservations,\n      refreshReservations,\n      handleFilterChange,\n      handleSortChange,\n      isActiveReservation,\n      showCheckInDialog,\n      showCheckOutDialog,\n      showCancelDialog,\n      showReservationDetail,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatDate,\n      formatDateTime,\n      calculateDuration,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.my-reservations {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  h2 {\n    margin: 0;\n  }\n}\n\n.filter-section {\n  margin-bottom: 20px;\n\n  .filter-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20px;\n\n    .filter-item {\n      display: flex;\n      align-items: center;\n\n      .filter-label {\n        margin-right: 10px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n.loading-container,\n.empty-container {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.reservation-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.reservation-card {\n  transition: transform 0.3s, box-shadow 0.3s;\n\n  &.reservation-active {\n    border-left: 4px solid #409eff;\n\n    &:hover {\n      transform: translateX(5px);\n    }\n  }\n\n  &:hover {\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n  }\n\n  .reservation-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .reservation-status {\n      display: flex;\n      align-items: center;\n      gap: 10px;\n\n      .reservation-id {\n        color: #909399;\n        font-size: 14px;\n      }\n    }\n\n    .reservation-time {\n      color: #909399;\n      font-size: 14px;\n    }\n  }\n\n  .reservation-content {\n    display: flex;\n    margin-bottom: 15px;\n\n    .seat-info {\n      flex: 1;\n\n      h3 {\n        margin: 0 0 5px 0;\n      }\n\n      .seat-location {\n        margin: 0 0 5px 0;\n        color: #606266;\n      }\n\n      .seat-number {\n        margin: 0;\n        display: flex;\n        align-items: center;\n        gap: 5px;\n      }\n    }\n\n    .time-info {\n      flex: 1;\n      text-align: right;\n\n      .time-label {\n        margin: 0 0 5px 0;\n        color: #909399;\n      }\n\n      .time-value {\n        margin: 0 0 5px 0;\n        font-weight: bold;\n      }\n\n      .time-duration {\n        margin: 0;\n        color: #606266;\n      }\n    }\n  }\n\n  .reservation-footer {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n  }\n}\n\n.check-in-dialog {\n  .qr-code-container {\n    display: flex;\n    justify-content: center;\n    margin-bottom: 20px;\n\n    .qr-code {\n      padding: 10px;\n      background-color: #fff;\n      border-radius: 4px;\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    }\n  }\n\n  .check-in-info {\n    text-align: center;\n    margin-bottom: 20px;\n\n    p {\n      margin: 5px 0;\n    }\n\n    .check-in-tip {\n      color: #909399;\n      font-size: 14px;\n      margin-top: 10px;\n    }\n  }\n\n  .manual-check-in {\n    text-align: center;\n    margin-top: 20px;\n  }\n}\n\n.check-out-dialog,\n.cancel-dialog {\n  p {\n    margin: 10px 0;\n  }\n\n  .cancel-warning {\n    margin: 20px 0;\n  }\n\n  .dialog-footer {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;EAQxBA,KAAK,EAAC;AAAgB;;EAElBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAa;;EAiBnBA,KAAK,EAAC;AAAa;;EAYnBA,KAAK,EAAC;AAAa;;EA5ClCC,GAAA;EA+DwBD,KAAK,EAAC;;;EA/D9BC,GAAA;EAoEuDD,KAAK,EAAC;;;EApE7DC,GAAA;EAwEgBD,KAAK,EAAC;;;EAOTA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAoB;;EAIvBA,KAAK,EAAC;AAAgB;;EAEzBA,KAAK,EAAC;AAAkB;;EAK1BA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAW;;EAEjBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAa;;EAmBnBA,KAAK,EAAC;AAAW;;EAEjBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAe;;EAQvBA,KAAK,EAAC;AAAoB;;EAhIvCC,GAAA;EA2JsCD,KAAK,EAAC;;;EAC/BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC,SAAS;EAACE,GAAG,EAAC;;;EAGtBF,KAAK,EAAC;AAAe;;EAOrBA,KAAK,EAAC;AAAiB;;EAvKpCC,GAAA;EAgLsCD,KAAK,EAAC;;;EAQ/BA,KAAK,EAAC;AAAe;;EAxLlCC,GAAA;EAuMsCD,KAAK,EAAC;;;EAQ/BA,KAAK,EAAC;AAAgB;;EAUtBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;uBAxNhCG,mBAAA,CAgOM,OAhONC,UAgOM,GA/NJC,mBAAA,CAQM,OARNC,UAQM,G,0BAPJD,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAKM,OALNE,UAKM,GAJJC,YAAA,CAGYC,oBAAA;IAHDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,MAAA,CAAAC;;IAL1CC,OAAA,EAAAC,QAAA,CAMU,MAA8B,CAA9BP,YAAA,CAA8BQ,kBAAA;MANxCF,OAAA,EAAAC,QAAA,CAMmB,MAAW,CAAXP,YAAA,CAAWS,kBAAA,E;MAN9BC,CAAA;kCAAAC,gBAAA,CAMwC,MAEhC,G;IARRD,CAAA;IAAAE,EAAA;sCAYIf,mBAAA,CAiDM,OAjDNgB,UAiDM,GAhDJb,YAAA,CA+CUc,kBAAA;IA/CDC,MAAM,EAAC;EAAO;IAb7BT,OAAA,EAAAC,QAAA,CAcQ,MA6CM,CA7CNV,mBAAA,CA6CM,OA7CNmB,UA6CM,GA5CJnB,mBAAA,CAeM,OAfNoB,UAeM,G,4BAdJpB,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BQ,YAAA,CAYYkB,oBAAA;MA7BxBC,UAAA,EAkBuBf,MAAA,CAAAgB,OAAO,CAACC,MAAM;MAlBrC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkBuBnB,MAAA,CAAAgB,OAAO,CAACC,MAAM,GAAAE,MAAA;MACvBC,WAAW,EAAC,MAAM;MAClBC,SAAS,EAAT,EAAS;MACRC,QAAM,EAAEtB,MAAA,CAAAuB;;MArBvBrB,OAAA,EAAAC,QAAA,CAwBgB,MAA+B,E,kBADjCZ,mBAAA,CAKEiC,SAAA,QA5BhBC,WAAA,CAwBiCzB,MAAA,CAAA0B,aAAa,EAAvBT,MAAM;6BADfU,YAAA,CAKEC,oBAAA;UAHCvC,GAAG,EAAE4B,MAAM,CAACY,KAAK;UACjBC,KAAK,EAAEb,MAAM,CAACa,KAAK;UACnBD,KAAK,EAAEZ,MAAM,CAACY;;;MA3B/BvB,CAAA;qDAgCUb,mBAAA,CAUM,OAVNsC,UAUM,G,4BATJtC,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BQ,YAAA,CAOEoC,yBAAA;MAzCdjB,UAAA,EAmCuBf,MAAA,CAAAgB,OAAO,CAACiB,IAAI;MAnCnC,uBAAAf,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAmCuBnB,MAAA,CAAAgB,OAAO,CAACiB,IAAI,GAAAd,MAAA;MACrBrB,IAAI,EAAC,MAAM;MACXsB,WAAW,EAAC,MAAM;MAClBc,MAAM,EAAC,YAAY;MACnB,cAAY,EAAC,YAAY;MACxBZ,QAAM,EAAEtB,MAAA,CAAAuB;2DAIb9B,mBAAA,CAcM,OAdN0C,UAcM,G,4BAbJ1C,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BQ,YAAA,CAWYkB,oBAAA;MAzDxBC,UAAA,EA+CuBf,MAAA,CAAAoC,MAAM;MA/C7B,uBAAAlB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA+CuBnB,MAAA,CAAAoC,MAAM,GAAAjB,MAAA;MACfC,WAAW,EAAC,MAAM;MACjBE,QAAM,EAAEtB,MAAA,CAAAqC;;MAjDvBnC,OAAA,EAAAC,QAAA,CAoDgB,MAA6B,E,kBAD/BZ,mBAAA,CAKEiC,SAAA,QAxDhBC,WAAA,CAoDiCzB,MAAA,CAAAsC,WAAW,EAArBC,MAAM;6BADfZ,YAAA,CAKEC,oBAAA;UAHCvC,GAAG,EAAEkD,MAAM,CAACV,KAAK;UACjBC,KAAK,EAAES,MAAM,CAACT,KAAK;UACnBD,KAAK,EAAEU,MAAM,CAACV;;;MAvD/BvB,CAAA;;IAAAA,CAAA;QA+DeN,MAAA,CAAAwC,OAAO,I,cAAlBjD,mBAAA,CAGM,OAHNkD,UAGM,GAFJ7C,YAAA,CAAkC8C,sBAAA;IAApBC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;MACvBhD,YAAA,CAA2D8C,sBAAA;IAA7CC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR,EAAQ;IAACC,KAAwB,EAAxB;MAAA;IAAA;UAGlB7C,MAAA,CAAA8C,oBAAoB,CAACC,MAAM,U,cAA3CxD,mBAAA,CAEM,OAFNyD,WAEM,GADJpD,YAAA,CAAwCqD,mBAAA;IAA9BC,WAAW,EAAC;EAAe,G,oBAGvC3D,mBAAA,CA+EM,OA/EN4D,WA+EM,I,kBA9EJ5D,mBAAA,CA6EUiC,SAAA,QAtJhBC,WAAA,CA0E8BzB,MAAA,CAAA8C,oBAAoB,EAAnCM,WAAW;yBADpBzB,YAAA,CA6EUjB,kBAAA;MA3EPrB,GAAG,EAAE+D,WAAW,CAACC,EAAE;MACpBjE,KAAK,EA5EbkE,eAAA,EA4Ec,kBAAkB;QAAA,sBACQtD,MAAA,CAAAuD,mBAAmB,CAACH,WAAW;MAAA;;MA7EvElD,OAAA,EAAAC,QAAA,CA+EQ,MAUM,CAVNV,mBAAA,CAUM,OAVN+D,WAUM,GATJ/D,mBAAA,CAKM,OALNgE,WAKM,GAJJ7D,YAAA,CAES8D,iBAAA;QAFA5D,IAAI,EAAEE,MAAA,CAAA2D,wBAAwB,CAACP,WAAW,CAACnC,MAAM;;QAjFtEf,OAAA,EAAAC,QAAA,CAkFc,MAAkD,CAlFhEI,gBAAA,CAAAqD,gBAAA,CAkFiB5D,MAAA,CAAA6D,wBAAwB,CAACT,WAAW,CAACnC,MAAM,kB;QAlF5DX,CAAA;qDAoFYb,mBAAA,CAA6D,QAA7DqE,WAA6D,EAAhC,OAAK,GAAAF,gBAAA,CAAGR,WAAW,CAACC,EAAE,iB,GAErD5D,mBAAA,CAEM,OAFNsE,WAEM,EAAAH,gBAAA,CADD5D,MAAA,CAAAgE,UAAU,CAACZ,WAAW,CAACa,UAAU,kB,GAIxCxE,mBAAA,CAmCM,OAnCNyE,WAmCM,GAlCJzE,mBAAA,CAoBM,OApBN0E,WAoBM,GAnBJ1E,mBAAA,CAAyC,YAAAmE,gBAAA,CAAlCR,WAAW,CAACgB,IAAI,CAACC,IAAI,CAACC,IAAI,kBACjC7E,mBAAA,CAAiE,KAAjE8E,WAAiE,EAAAX,gBAAA,CAArCR,WAAW,CAACgB,IAAI,CAACC,IAAI,CAACG,QAAQ,kBAC1D/E,mBAAA,CAgBI,KAhBJgF,WAgBI,GA/GhBlE,gBAAA,CA+FmC,QAChB,GAAAqD,gBAAA,CAAGR,WAAW,CAACgB,IAAI,CAACM,WAAW,IAAG,GACvC,iBACQtB,WAAW,CAACgB,IAAI,CAACO,eAAe,I,cADxChD,YAAA,CAMS+B,iBAAA;QAvGvBrE,GAAA;QAmGgBuF,IAAI,EAAC,OAAO;QACZC,MAAM,EAAC;;QApGvB3E,OAAA,EAAAC,QAAA,CAqGe,MAED,KAAAe,MAAA,SAAAA,MAAA,QAvGdX,gBAAA,CAqGe,OAED,E;QAvGdD,CAAA;QAAAE,EAAA;YAAAsE,mBAAA,gBAyGsB1B,WAAW,CAACgB,IAAI,CAACW,cAAc,I,cADvCpD,YAAA,CAMS+B,iBAAA;QA9GvBrE,GAAA;QA0GgBuF,IAAI,EAAC,OAAO;QACZC,MAAM,EAAC;;QA3GvB3E,OAAA,EAAAC,QAAA,CA4Ge,MAED,KAAAe,MAAA,SAAAA,MAAA,QA9GdX,gBAAA,CA4Ge,MAED,E;QA9GdD,CAAA;QAAAE,EAAA;YAAAsE,mBAAA,e,KAkHUrF,mBAAA,CAWM,OAXNuF,WAWM,G,4BAVJvF,mBAAA,CAA8B;QAA3BL,KAAK,EAAC;MAAY,GAAC,MAAI,sBAC1BK,mBAAA,CAGI,KAHJwF,WAGI,EAAArB,gBAAA,CAFC5D,MAAA,CAAAkF,cAAc,CAAC9B,WAAW,CAAC+B,UAAU,KAAI,KAC5C,GAAAvB,gBAAA,CAAG5D,MAAA,CAAAkF,cAAc,CAAC9B,WAAW,CAACgC,QAAQ,mBAExC3F,mBAAA,CAII,KAJJ4F,WAII,EAAAzB,gBAAA,CAFA5D,MAAA,CAAAsF,iBAAiB,CAAClC,WAAW,CAAC+B,UAAU,EAAE/B,WAAW,CAACgC,QAAQ,kB,KAMtE3F,mBAAA,CAqBM,OArBN8F,WAqBM,GApBYnC,WAAW,CAACnC,MAAM,kB,cAAlC1B,mBAAA,CAOWiC,SAAA;QAxIrBnC,GAAA;MAAA,IAkIYO,YAAA,CAECC,oBAAA;QAFUC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAoB,MAAA,IAAEnB,MAAA,CAAAwF,iBAAiB,CAACpC,WAAW;;QAlI3ElD,OAAA,EAAAC,QAAA,CAmIe,MAAE,KAAAe,MAAA,SAAAA,MAAA,QAnIjBX,gBAAA,CAmIe,IAAE,E;QAnIjBD,CAAA;QAAAE,EAAA;wDAqIYZ,YAAA,CAECC,oBAAA;QAFUC,IAAI,EAAC,QAAQ;QAAEC,OAAK,EAAAoB,MAAA,IAAEnB,MAAA,CAAAyF,gBAAgB,CAACrC,WAAW;;QArIzElD,OAAA,EAAAC,QAAA,CAsIe,MAAI,KAAAe,MAAA,SAAAA,MAAA,QAtInBX,gBAAA,CAsIe,MAAI,E;QAtInBD,CAAA;QAAAE,EAAA;sFA0I+B4C,WAAW,CAACnC,MAAM,qB,cACrCU,YAAA,CAEC9B,oBAAA;QA7IbR,GAAA;QA2IuBS,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAoB,MAAA,IAAEnB,MAAA,CAAA0F,kBAAkB,CAACtC,WAAW;;QA3I5ElD,OAAA,EAAAC,QAAA,CA4Ie,MAAE,KAAAe,MAAA,SAAAA,MAAA,QA5IjBX,gBAAA,CA4Ie,IAAE,E;QA5IjBD,CAAA;QAAAE,EAAA;yEAiJYmB,YAAA,CAEC9B,oBAAA;QAnJbR,GAAA;QAiJuBS,IAAI,EAAC,MAAM;QAAEC,OAAK,EAAAoB,MAAA,IAAEnB,MAAA,CAAA2F,qBAAqB,CAACvC,WAAW;;QAjJ5ElD,OAAA,EAAAC,QAAA,CAkJe,MAAI,KAAAe,MAAA,SAAAA,MAAA,QAlJnBX,gBAAA,CAkJe,MAAI,E;QAlJnBD,CAAA;QAAAE,EAAA;;MAAAF,CAAA;;qCAyJIwE,mBAAA,WAAc,EACdlF,YAAA,CAkBYgG,oBAAA;IA5KhB7E,UAAA,EA0JwBf,MAAA,CAAA6F,oBAAoB;IA1J5C,uBAAA3E,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA0JwBnB,MAAA,CAAA6F,oBAAoB,GAAA1E,MAAA;IAAE2E,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC;;IA1JjE7F,OAAA,EAAAC,QAAA,CAyDi0H,MAA+nB,CAkG/6IH,MAAA,CAAAgG,mBAAmB,I,cAA9BzG,mBAAA,CAgBM,OAhBN0G,WAgBM,GAfJxG,mBAAA,CAEM,OAFNyG,WAEM,GADJzG,mBAAA,CAA2C,OAA3C0G,WAA2C,8B,GAG7C1G,mBAAA,CAKM,OALN2G,WAKM,GAJJ3G,mBAAA,CAAwC,WAArC,OAAK,GAAAmE,gBAAA,CAAG5D,MAAA,CAAAgG,mBAAmB,CAAC3C,EAAE,kBACjC5D,mBAAA,CAAuD,WAApD,OAAK,GAAAmE,gBAAA,CAAG5D,MAAA,CAAAgG,mBAAmB,CAAC5B,IAAI,EAAEM,WAAW,kBAChDjF,mBAAA,CAAsD,WAAnD,OAAK,GAAAmE,gBAAA,CAAG5D,MAAA,CAAAgG,mBAAmB,CAACK,gBAAgB,kB,4BAC/C5G,mBAAA,CAA6C;MAA1CL,KAAK,EAAC;IAAc,GAAC,mBAAiB,qB,GAG3CK,mBAAA,CAGM,OAHN6G,WAGM,GAFJ1G,YAAA,CAA8B2G,qBAAA;MAxKxCrG,OAAA,EAAAC,QAAA,CAwKsB,MAAKe,MAAA,SAAAA,MAAA,QAxK3BX,gBAAA,CAwKsB,OAAK,E;MAxK3BD,CAAA;MAAAE,EAAA;QAyKUZ,YAAA,CAAiEC,oBAAA;MAAtDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,MAAA,CAAAwG;;MAzK5CtG,OAAA,EAAAC,QAAA,CAyK2D,MAAIe,MAAA,SAAAA,MAAA,QAzK/DX,gBAAA,CAyK2D,MAAI,E;MAzK/DD,CAAA;MAAAE,EAAA;0CAAAsE,mBAAA,e;IAAAxE,CAAA;qCA8KIwE,mBAAA,WAAc,EACdlF,YAAA,CAoBYgG,oBAAA;IAnMhB7E,UAAA,EA+KwBf,MAAA,CAAAyG,qBAAqB;IA/K7C,uBAAAvF,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA+KwBnB,MAAA,CAAAyG,qBAAqB,GAAAtF,MAAA;IAAE2E,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC;;IA/KlE7F,OAAA,EAAAC,QAAA,CAyDujJ,MAAsmB,CAuH5oKH,MAAA,CAAAgG,mBAAmB,I,cAA9BzG,mBAAA,CAkBM,OAlBNmH,WAkBM,G,4BAjBJjH,mBAAA,CAAiB,WAAd,YAAU,sBACbA,mBAAA,CAAuD,WAApD,OAAK,GAAAmE,gBAAA,CAAG5D,MAAA,CAAAgG,mBAAmB,CAAC5B,IAAI,EAAEM,WAAW,kBAChDjF,mBAAA,CAGI,WAHD,SACK,GAAAmE,gBAAA,CAAG5D,MAAA,CAAAkF,cAAc,CAAClF,MAAA,CAAAgG,mBAAmB,CAACb,UAAU,KAAI,KAC1D,GAAAvB,gBAAA,CAAG5D,MAAA,CAAAkF,cAAc,CAAClF,MAAA,CAAAgG,mBAAmB,CAACZ,QAAQ,mBAGhD3F,mBAAA,CASM,OATNkH,WASM,GARJ/G,YAAA,CAAgEC,oBAAA;MAApDE,OAAK,EAAAmB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEnB,MAAA,CAAAyG,qBAAqB;;MAzLlDvG,OAAA,EAAAC,QAAA,CAyL4D,MAAEe,MAAA,SAAAA,MAAA,QAzL9DX,gBAAA,CAyL4D,IAAE,E;MAzL9DD,CAAA;MAAAE,EAAA;QA0LUZ,YAAA,CAMYC,oBAAA;MALVC,IAAI,EAAC,SAAS;MACbC,OAAK,EAAEC,MAAA,CAAA4G,cAAc;MACrBpE,OAAO,EAAExC,MAAA,CAAA6G;;MA7LtB3G,OAAA,EAAAC,QAAA,CA8LW,MAEDe,MAAA,SAAAA,MAAA,QAhMVX,gBAAA,CA8LW,QAED,E;MAhMVD,CAAA;MAAAE,EAAA;qDAAAsE,mBAAA,e;IAAAxE,CAAA;qCAqMIwE,mBAAA,aAAgB,EAChBlF,YAAA,CA0BYgG,oBAAA;IAhOhB7E,UAAA,EAsMwBf,MAAA,CAAA8G,mBAAmB;IAtM3C,uBAAA5F,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAsMwBnB,MAAA,CAAA8G,mBAAmB,GAAA3F,MAAA;IAAE2E,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC;;IAtMhE7F,OAAA,EAAAC,QAAA,CAyDoxK,MAAuyB,CA8I1iMH,MAAA,CAAAgG,mBAAmB,I,cAA9BzG,mBAAA,CAwBM,OAxBNwH,WAwBM,G,4BAvBJtH,mBAAA,CAAkB,WAAf,aAAW,sBACdA,mBAAA,CAAuD,WAApD,OAAK,GAAAmE,gBAAA,CAAG5D,MAAA,CAAAgG,mBAAmB,CAAC5B,IAAI,EAAEM,WAAW,kBAChDjF,mBAAA,CAGI,WAHD,SACK,GAAAmE,gBAAA,CAAG5D,MAAA,CAAAkF,cAAc,CAAClF,MAAA,CAAAgG,mBAAmB,CAACb,UAAU,KAAI,KAC1D,GAAAvB,gBAAA,CAAG5D,MAAA,CAAAkF,cAAc,CAAClF,MAAA,CAAAgG,mBAAmB,CAACZ,QAAQ,mBAGhD3F,mBAAA,CAQM,OARNuH,WAQM,GAPJpH,YAAA,CAMEqH,mBAAA;MALAnB,KAAK,EAAC,QAAQ;MACdhG,IAAI,EAAC,SAAS;MACdoD,WAAW,EAAC,8BAA8B;MAC1C,WAAS,EAAT,EAAS;MACRgE,QAAQ,EAAE;UAIfzH,mBAAA,CAKM,OALN0H,WAKM,GAJJvH,YAAA,CAA8DC,oBAAA;MAAlDE,OAAK,EAAAmB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEnB,MAAA,CAAA8G,mBAAmB;;MA1NhD5G,OAAA,EAAAC,QAAA,CA0N0D,MAAEe,MAAA,SAAAA,MAAA,QA1N5DX,gBAAA,CA0N0D,IAAE,E;MA1N5DD,CAAA;MAAAE,EAAA;QA2NUZ,YAAA,CAECC,oBAAA;MAFUC,IAAI,EAAC,QAAQ;MAAEC,OAAK,EAAEC,MAAA,CAAAoH,YAAY;MAAG5E,OAAO,EAAExC,MAAA,CAAA6G;;MA3NnE3G,OAAA,EAAAC,QAAA,CA4Na,MAAIe,MAAA,SAAAA,MAAA,QA5NjBX,gBAAA,CA4Na,MAAI,E;MA5NjBD,CAAA;MAAAE,EAAA;qDAAAsE,mBAAA,e;IAAAxE,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}