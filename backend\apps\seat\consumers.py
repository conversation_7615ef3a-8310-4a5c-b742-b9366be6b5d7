"""
WebSocket消费者
"""
import json
import logging
from channels.generic.websocket import AsyncJsonWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from rest_framework_simplejwt.tokens import UntokenedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.contrib.auth import get_user_model
from .models import Room, Seat, Reservation
from utils.crypto import SM4Crypto
from django.conf import settings

logger = logging.getLogger(__name__)
User = get_user_model()


class SeatStatusConsumer(AsyncJsonWebsocketConsumer):
    """座位状态WebSocket消费者"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.room_id = None
        self.room_group_name = None
        self.user = None
        self.session_key = None
    
    async def connect(self):
        """WebSocket连接建立"""
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f'seat_room_{self.room_id}'
        
        # 验证房间是否存在
        room_exists = await self.check_room_exists(self.room_id)
        if not room_exists:
            await self.close(code=4004)
            return
        
        # 加入房间组
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # 发送连接成功消息
        await self.send_json({
            'type': 'connection_established',
            'message': f'已连接到自习室 {self.room_id} 的座位状态频道'
        })
    
    async def disconnect(self, close_code):
        """WebSocket连接断开"""
        if self.room_group_name:
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )
    
    async def receive_json(self, content):
        """接收WebSocket消息"""
        message_type = content.get('type')
        
        if message_type == 'authenticate':
            await self.handle_authentication(content)
        elif message_type == 'subscribe':
            await self.handle_subscription(content)
        elif message_type == 'encrypted':
            await self.handle_encrypted_message(content)
        else:
            await self.send_json({
                'type': 'error',
                'message': f'未知的消息类型: {message_type}'
            })
    
    async def handle_authentication(self, content):
        """处理认证"""
        token = content.get('token')
        if not token:
            await self.send_json({
                'type': 'auth_error',
                'message': '缺少认证令牌'
            })
            return
        
        try:
            # 验证JWT令牌
            untoken = UntokenedToken(token)
            user_id = untoken.get('user_id')
            
            # 获取用户
            self.user = await self.get_user(user_id)
            if not self.user:
                await self.send_json({
                    'type': 'auth_error',
                    'message': '用户不存在'
                })
                return
            
            # 生成会话密钥
            self.session_key = SM4Crypto.generate_key()
            
            await self.send_json({
                'type': 'auth_success',
                'message': '认证成功',
                'user_id': self.user.id
            })
            
        except (InvalidToken, TokenError) as e:
            await self.send_json({
                'type': 'auth_error',
                'message': '无效的认证令牌'
            })
    
    async def handle_subscription(self, content):
        """处理订阅请求"""
        if not self.user:
            await self.send_json({
                'type': 'error',
                'message': '请先进行认证'
            })
            return
        
        # 发送当前座位状态
        seats_data = await self.get_room_seats_status(self.room_id)
        await self.send_json({
            'type': 'seat_status_snapshot',
            'data': seats_data
        })
    
    async def handle_encrypted_message(self, content):
        """处理加密消息"""
        if not self.user or not self.session_key:
            await self.send_json({
                'type': 'error',
                'message': '请先进行认证'
            })
            return
        
        try:
            encrypted_data = content.get('data', '')
            decrypted_data = SM4Crypto.decrypt(self.session_key, encrypted_data)
            actual_message = json.loads(decrypted_data)
            
            # 处理解密后的消息
            await self.handle_decrypted_message(actual_message)
            
        except Exception as e:
            logger.error(f"WebSocket消息解密失败: {str(e)}")
            await self.send_json({
                'type': 'error',
                'message': '消息解密失败'
            })
    
    async def handle_decrypted_message(self, message):
        """处理解密后的消息"""
        message_type = message.get('type')
        
        if message_type == 'get_seat_detail':
            seat_id = message.get('seat_id')
            seat_data = await self.get_seat_detail(seat_id)
            await self.send_encrypted_json({
                'type': 'seat_detail',
                'data': seat_data
            })
    
    async def send_encrypted_json(self, content):
        """发送加密JSON消息"""
        if self.session_key:
            encrypted_data = SM4Crypto.encrypt(self.session_key, json.dumps(content))
            await self.send_json({
                'type': 'encrypted',
                'data': encrypted_data
            })
        else:
            await self.send_json(content)
    
    # 座位状态更新事件处理器
    async def seat_status_update(self, event):
        """处理座位状态更新事件"""
        await self.send_json({
            'type': 'seat_status_update',
            'data': event['data']
        })
    
    async def reservation_update(self, event):
        """处理预约更新事件"""
        await self.send_json({
            'type': 'reservation_update',
            'data': event['data']
        })
    
    # 数据库查询方法
    @database_sync_to_async
    def check_room_exists(self, room_id):
        """检查房间是否存在"""
        try:
            Room.objects.get(id=room_id)
            return True
        except Room.DoesNotExist:
            return False
    
    @database_sync_to_async
    def get_user(self, user_id):
        """获取用户"""
        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            return None
    
    @database_sync_to_async
    def get_room_seats_status(self, room_id):
        """获取房间座位状态"""
        try:
            room = Room.objects.get(id=room_id)
            seats = room.seat_set.all()
            
            seats_data = []
            for seat in seats:
                # 获取当前预约
                current_reservation = seat.reservation_set.filter(
                    status__in=['pending', 'checked_in']
                ).first()
                
                seat_data = {
                    'id': seat.id,
                    'seat_number': seat.seat_number,
                    'row': seat.row,
                    'column': seat.column,
                    'status': seat.status,
                    'is_power_outlet': seat.is_power_outlet,
                    'is_window_seat': seat.is_window_seat,
                    'current_reservation': None
                }
                
                if current_reservation:
                    seat_data['current_reservation'] = {
                        'id': current_reservation.id,
                        'status': current_reservation.status,
                        'start_time': current_reservation.start_time.isoformat(),
                        'end_time': current_reservation.end_time.isoformat(),
                    }
                
                seats_data.append(seat_data)
            
            return seats_data
        except Room.DoesNotExist:
            return []
    
    @database_sync_to_async
    def get_seat_detail(self, seat_id):
        """获取座位详情"""
        try:
            seat = Seat.objects.get(id=seat_id)
            reservations = seat.reservation_set.filter(
                status__in=['pending', 'checked_in']
            ).order_by('start_time')
            
            reservations_data = []
            for reservation in reservations:
                reservations_data.append({
                    'id': reservation.id,
                    'status': reservation.status,
                    'start_time': reservation.start_time.isoformat(),
                    'end_time': reservation.end_time.isoformat(),
                })
            
            return {
                'id': seat.id,
                'seat_number': seat.seat_number,
                'status': seat.status,
                'reservations': reservations_data
            }
        except Seat.DoesNotExist:
            return None


class NotificationConsumer(AsyncJsonWebsocketConsumer):
    """通知WebSocket消费者"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = None
        self.user_group_name = None
    
    async def connect(self):
        """WebSocket连接建立"""
        await self.accept()
        
        # 发送连接成功消息
        await self.send_json({
            'type': 'connection_established',
            'message': '已连接到通知频道'
        })
    
    async def disconnect(self, close_code):
        """WebSocket连接断开"""
        if self.user_group_name:
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )
    
    async def receive_json(self, content):
        """接收WebSocket消息"""
        message_type = content.get('type')
        
        if message_type == 'authenticate':
            await self.handle_authentication(content)
        else:
            await self.send_json({
                'type': 'error',
                'message': f'未知的消息类型: {message_type}'
            })
    
    async def handle_authentication(self, content):
        """处理认证"""
        token = content.get('token')
        if not token:
            await self.send_json({
                'type': 'auth_error',
                'message': '缺少认证令牌'
            })
            return
        
        try:
            # 验证JWT令牌
            untoken = UntokenedToken(token)
            user_id = untoken.get('user_id')
            
            # 获取用户
            self.user = await self.get_user(user_id)
            if not self.user:
                await self.send_json({
                    'type': 'auth_error',
                    'message': '用户不存在'
                })
                return
            
            # 加入用户组
            self.user_group_name = f'user_{self.user.id}'
            await self.channel_layer.group_add(
                self.user_group_name,
                self.channel_name
            )
            
            await self.send_json({
                'type': 'auth_success',
                'message': '认证成功',
                'user_id': self.user.id
            })
            
        except (InvalidToken, TokenError) as e:
            await self.send_json({
                'type': 'auth_error',
                'message': '无效的认证令牌'
            })
    
    # 通知事件处理器
    async def reservation_reminder(self, event):
        """处理预约提醒事件"""
        await self.send_json({
            'type': 'reservation_reminder',
            'data': event['data']
        })
    
    async def system_notification(self, event):
        """处理系统通知事件"""
        await self.send_json({
            'type': 'system_notification',
            'data': event['data']
        })
    
    @database_sync_to_async
    def get_user(self, user_id):
        """获取用户"""
        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            return None
