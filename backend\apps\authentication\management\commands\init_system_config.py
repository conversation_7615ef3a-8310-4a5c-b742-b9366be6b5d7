"""
初始化系统配置的管理命令
"""
from django.core.management.base import BaseCommand
from apps.authentication.models import SystemConfig
from utils.crypto import SM4Crypto


class Command(BaseCommand):
    help = '初始化系统配置'

    def handle(self, *args, **options):
        """执行命令"""
        self.stdout.write('开始初始化系统配置...')
        
        # 初始化默认配置
        configs = [
            {
                'key': 'sm4_encryption_key',
                'value': SM4Crypto.generate_key(),
                'config_type': 'encryption',
                'description': '系统SM4加密密钥',
                'is_encrypted': True
            },
            {
                'key': 'password_reset_token_expiry',
                'value': '3600',
                'config_type': 'security',
                'description': '密码重置令牌有效期（秒）'
            },
            {
                'key': 'max_login_attempts',
                'value': '5',
                'config_type': 'security',
                'description': '最大登录尝试次数'
            },
            {
                'key': 'session_timeout',
                'value': '86400',
                'config_type': 'security',
                'description': '会话超时时间（秒）'
            },
            {
                'key': 'system_name',
                'value': '基于国密算法的图书馆自习室座位管理系统',
                'config_type': 'system',
                'description': '系统名称'
            },
            {
                'key': 'system_version',
                'value': '1.0.0',
                'config_type': 'system',
                'description': '系统版本'
            },
            {
                'key': 'email_settings',
                'value': {
                    'smtp_host': 'smtp.example.com',
                    'smtp_port': 587,
                    'smtp_user': '',
                    'smtp_password': '',
                    'use_tls': True,
                    'from_email': '<EMAIL>'
                },
                'config_type': 'email',
                'description': '邮件服务器配置'
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for config_data in configs:
            config, created = SystemConfig.objects.get_or_create(
                key=config_data['key'],
                defaults={
                    'config_type': config_data['config_type'],
                    'description': config_data['description'],
                    'is_encrypted': config_data.get('is_encrypted', False)
                }
            )
            
            if created:
                config.set_value(config_data['value'])
                config.save()
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ 创建配置: {config_data["key"]}')
                )
            else:
                # 如果配置已存在，检查是否需要更新
                if not config.value:
                    config.set_value(config_data['value'])
                    config.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'⚠ 更新配置: {config_data["key"]}')
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'- 配置已存在: {config_data["key"]}')
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n系统配置初始化完成！'
                f'\n创建了 {created_count} 个新配置'
                f'\n更新了 {updated_count} 个配置'
            )
        )
