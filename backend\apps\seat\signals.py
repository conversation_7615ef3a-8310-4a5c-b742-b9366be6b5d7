"""
座位管理信号处理器
用于实时推送座位状态更新
"""
import json
import logging
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from .models import Seat, Reservation

logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()


@receiver(post_save, sender=Seat)
def seat_status_changed(sender, instance, created, **kwargs):
    """座位状态变化时发送WebSocket消息"""
    if not created:  # 只处理更新，不处理创建
        try:
            # 获取当前预约
            current_reservation = instance.reservation_set.filter(
                status__in=['pending', 'checked_in']
            ).first()
            
            seat_data = {
                'id': instance.id,
                'seat_number': instance.seat_number,
                'row': instance.row,
                'column': instance.column,
                'status': instance.status,
                'is_power_outlet': instance.is_power_outlet,
                'is_window_seat': instance.is_window_seat,
                'current_reservation': None
            }
            
            if current_reservation:
                seat_data['current_reservation'] = {
                    'id': current_reservation.id,
                    'status': current_reservation.status,
                    'start_time': current_reservation.start_time.isoformat(),
                    'end_time': current_reservation.end_time.isoformat(),
                }
            
            # 发送到房间组
            room_group_name = f'seat_room_{instance.room.id}'
            async_to_sync(channel_layer.group_send)(
                room_group_name,
                {
                    'type': 'seat_status_update',
                    'data': seat_data
                }
            )
            
            logger.info(f"座位状态更新推送: 座位 {instance.seat_number} 状态变为 {instance.status}")
            
        except Exception as e:
            logger.error(f"推送座位状态更新失败: {str(e)}")


@receiver(post_save, sender=Reservation)
def reservation_status_changed(sender, instance, created, **kwargs):
    """预约状态变化时发送WebSocket消息"""
    try:
        reservation_data = {
            'id': instance.id,
            'seat_id': instance.seat.id,
            'seat_number': instance.seat.seat_number,
            'room_id': instance.seat.room.id,
            'room_name': instance.seat.room.name,
            'status': instance.status,
            'start_time': instance.start_time.isoformat(),
            'end_time': instance.end_time.isoformat(),
            'check_in_time': instance.check_in_time.isoformat() if instance.check_in_time else None,
            'check_out_time': instance.check_out_time.isoformat() if instance.check_out_time else None,
        }
        
        # 发送到房间组
        room_group_name = f'seat_room_{instance.seat.room.id}'
        async_to_sync(channel_layer.group_send)(
            room_group_name,
            {
                'type': 'reservation_update',
                'data': reservation_data
            }
        )
        
        # 发送到用户组（个人通知）
        user_group_name = f'user_{instance.user.id}'
        async_to_sync(channel_layer.group_send)(
            user_group_name,
            {
                'type': 'reservation_update',
                'data': reservation_data
            }
        )
        
        # 如果是新创建的预约，发送预约成功通知
        if created:
            async_to_sync(channel_layer.group_send)(
                user_group_name,
                {
                    'type': 'system_notification',
                    'data': {
                        'title': '预约成功',
                        'message': f'您已成功预约 {instance.seat.room.name} 的 {instance.seat.seat_number} 座位',
                        'type': 'success',
                        'reservation_id': instance.id
                    }
                }
            )
        
        # 根据状态发送不同通知
        if instance.status == 'checked_in':
            async_to_sync(channel_layer.group_send)(
                user_group_name,
                {
                    'type': 'system_notification',
                    'data': {
                        'title': '签到成功',
                        'message': f'您已成功签到 {instance.seat.room.name} 的 {instance.seat.seat_number} 座位',
                        'type': 'success',
                        'reservation_id': instance.id
                    }
                }
            )
        elif instance.status == 'completed':
            async_to_sync(channel_layer.group_send)(
                user_group_name,
                {
                    'type': 'system_notification',
                    'data': {
                        'title': '签退成功',
                        'message': f'您已成功签退 {instance.seat.room.name} 的 {instance.seat.seat_number} 座位',
                        'type': 'success',
                        'reservation_id': instance.id
                    }
                }
            )
        elif instance.status == 'cancelled':
            async_to_sync(channel_layer.group_send)(
                user_group_name,
                {
                    'type': 'system_notification',
                    'data': {
                        'title': '预约已取消',
                        'message': f'您的 {instance.seat.room.name} {instance.seat.seat_number} 座位预约已取消',
                        'type': 'warning',
                        'reservation_id': instance.id
                    }
                }
            )
        elif instance.status == 'timeout':
            async_to_sync(channel_layer.group_send)(
                user_group_name,
                {
                    'type': 'system_notification',
                    'data': {
                        'title': '预约已超时',
                        'message': f'您的 {instance.seat.room.name} {instance.seat.seat_number} 座位预约因未及时签到已超时',
                        'type': 'error',
                        'reservation_id': instance.id
                    }
                }
            )
        
        logger.info(f"预约状态更新推送: 预约 {instance.id} 状态变为 {instance.status}")
        
    except Exception as e:
        logger.error(f"推送预约状态更新失败: {str(e)}")


def send_reservation_reminder(user_id, reservation_data):
    """发送预约提醒"""
    try:
        user_group_name = f'user_{user_id}'
        async_to_sync(channel_layer.group_send)(
            user_group_name,
            {
                'type': 'reservation_reminder',
                'data': reservation_data
            }
        )
        logger.info(f"发送预约提醒: 用户 {user_id}, 预约 {reservation_data.get('id')}")
    except Exception as e:
        logger.error(f"发送预约提醒失败: {str(e)}")


def send_system_notification(user_id, notification_data):
    """发送系统通知"""
    try:
        user_group_name = f'user_{user_id}'
        async_to_sync(channel_layer.group_send)(
            user_group_name,
            {
                'type': 'system_notification',
                'data': notification_data
            }
        )
        logger.info(f"发送系统通知: 用户 {user_id}, 通知: {notification_data.get('title')}")
    except Exception as e:
        logger.error(f"发送系统通知失败: {str(e)}")
