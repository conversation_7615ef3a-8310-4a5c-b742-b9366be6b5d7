"""
密钥管理工具类
提供SM4密钥生成、存储和轮换功能
"""
import os
import uuid
import logging
from django.db import models, transaction
from django.utils import timezone
from django.conf import settings
from gmssl import sm2

logger = logging.getLogger(__name__)

class SM4Key(models.Model):
    """SM4密钥表"""
    id = models.CharField(max_length=36, primary_key=True, verbose_name='密钥ID')
    encrypted_key = models.BinaryField(max_length=128, verbose_name='SM2加密的SM4密钥')
    active = models.BooleanField(default=False, verbose_name='是否为当前活跃密钥')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    activated_at = models.DateTimeField(null=True, blank=True, verbose_name='激活时间')
    deactivated_at = models.DateTimeField(null=True, blank=True, verbose_name='停用时间')

    class Meta:
        db_table = 'sm4_key'
        verbose_name = 'SM4密钥'
        verbose_name_plural = 'SM4密钥'
        indexes = [
            models.Index(fields=['active']),
        ]

    def __str__(self):
        return f"SM4Key {self.id} - Active: {self.active}"


def log_security_event(event_type, description, level='info'):
    """记录安全事件"""
    
    if level == 'info':
        logger.info(f"安全事件 [{event_type}]: {description}")
    elif level == 'warning':
        logger.warning(f"安全事件 [{event_type}]: {description}")
    elif level == 'error':
        logger.error(f"安全事件 [{event_type}]: {description}")


class KeyManager:
    """SM4密钥管理器"""
    
    @classmethod
    def generate_key(cls):
        """
        生成新的SM4密钥
        
        返回:
            key_id: 密钥ID
        """
        # 生成16字节(128位)的随机密钥
        new_key = os.urandom(16)
        key_id = str(uuid.uuid4())
        
        try:
            # 使用SM2加密密钥后存储
            # 从配置文件读取系统SM2公钥
            with open(settings.SM2_PUBLIC_KEY_PATH, 'rb') as f:
                public_key = f.read()
            
            sm2_crypt = sm2.CryptSM2(
                public_key=public_key,
                private_key=None
            )
            encrypted_key = sm2_crypt.encrypt(new_key)
            
            # 存储加密的密钥
            SM4Key.objects.create(
                id=key_id,
                encrypted_key=encrypted_key,
                active=False
            )
            
            logger.info(f"生成新SM4密钥: {key_id}")
            return key_id
        except Exception as e:
            logger.error(f"生成SM4密钥失败: {str(e)}")
            raise RuntimeError("密钥生成失败") from e
    
    @classmethod
    def activate_key(cls, key_id):
        """
        激活指定密钥
        
        参数:
            key_id: 密钥ID
        """
        try:
            with transaction.atomic():
                # 将所有密钥设为非活跃
                SM4Key.objects.filter(active=True).update(
                    active=False,
                    deactivated_at=timezone.now()
                )
                
                # 激活指定密钥
                key = SM4Key.objects.get(id=key_id)
                key.active = True
                key.activated_at = timezone.now()
                key.save()
                
                # 记录密钥轮换事件
                log_security_event('key_rotation', f"密钥轮换: {key_id}", 'info')
                logger.info(f"激活SM4密钥: {key_id}")
        except Exception as e:
            logger.error(f"激活SM4密钥失败: {str(e)}")
            raise RuntimeError("密钥激活失败") from e
    
    @classmethod
    def get_active_key(cls):
        """
        获取当前活跃的SM4密钥
        
        返回:
            decrypted_key: 解密后的SM4密钥
        """
        try:
            key = SM4Key.objects.get(active=True)
            
            # 从配置文件读取系统SM2私钥
            with open(settings.SM2_PRIVATE_KEY_PATH, 'rb') as f:
                private_key = f.read()
            
            # 使用SM2解密密钥
            sm2_crypt = sm2.CryptSM2(
                public_key=None,
                private_key=private_key
            )
            decrypted_key = sm2_crypt.decrypt(key.encrypted_key)
            
            return decrypted_key
        except SM4Key.DoesNotExist:
            # 如果没有活跃密钥，自动创建并激活一个新密钥
            logger.warning("未找到活跃的SM4密钥，自动创建新密钥")
            key_id = cls.generate_key()
            cls.activate_key(key_id)
            return cls.get_active_key()
        except Exception as e:
            logger.error(f"获取活跃SM4密钥失败: {str(e)}")
            raise RuntimeError("获取密钥失败") from e
    
    @classmethod
    def rotate_keys(cls):
        """
        执行密钥轮换
        
        返回:
            new_key_id: 新密钥ID
        """
        try:
            # 生成新密钥
            new_key_id = cls.generate_key()
            
            # 激活新密钥
            cls.activate_key(new_key_id)
            
            # 删除过期密钥(超过90天的非活跃密钥)
            expiry_date = timezone.now() - timezone.timedelta(days=90)
            old_keys = SM4Key.objects.filter(
                active=False,
                deactivated_at__lt=expiry_date
            )
            
            if old_keys.exists():
                log_security_event(
                    'key_deletion', 
                    f"删除过期密钥: {[k.id for k in old_keys]}", 
                    'info'
                )
                old_keys.delete()
            
            logger.info(f"完成SM4密钥轮换，新密钥ID: {new_key_id}")
            return new_key_id
        except Exception as e:
            logger.error(f"SM4密钥轮换失败: {str(e)}")
            raise RuntimeError("密钥轮换失败") from e
    
    @classmethod
    def decrypt_with_key_id(cls, encrypted_data, key_id):
        """
        使用指定ID的密钥解密数据
        
        参数:
            encrypted_data: 加密数据
            key_id: 密钥ID
            
        返回:
            解密后的数据
        """
        from utils.crypto import SM4Crypto
        
        try:
            # 获取指定密钥
            key = SM4Key.objects.get(id=key_id)
            
            # 从配置文件读取系统SM2私钥
            with open(settings.SM2_PRIVATE_KEY_PATH, 'rb') as f:
                private_key = f.read()
            
            # 使用SM2解密SM4密钥
            sm2_crypt = sm2.CryptSM2(
                public_key=None,
                private_key=private_key
            )
            sm4_key = sm2_crypt.decrypt(key.encrypted_key)
            
            # 使用SM4密钥解密数据
            return SM4Crypto.decrypt(sm4_key, encrypted_data)
        except SM4Key.DoesNotExist:
            logger.error(f"未找到ID为{key_id}的密钥")
            raise ValueError(f"未找到指定的密钥: {key_id}")
        except Exception as e:
            logger.error(f"使用指定密钥解密失败: {str(e)}")
            raise RuntimeError("解密操作失败") from e
