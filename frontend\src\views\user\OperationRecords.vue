<template>
  <div class="operation-records-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>操作记录</h2>
        </div>
      </template>

      <el-table :data="records" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="operation_type" label="操作类型" width="120" />
        <el-table-column prop="operation_time" label="操作时间" width="180" />
        <el-table-column prop="operation_content" label="操作内容" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted } from "vue";

export default {
  name: "OperationRecords",
  setup() {
    const records = ref([]);
    const loading = ref(true);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);

    const fetchRecords = async () => {
      try {
        loading.value = true;
        // 这里应该调用用户操作记录API
        // 暂时使用空数组，等待后端API实现
        records.value = [];
        total.value = 0;
      } catch (error) {
        console.error("获取操作记录失败:", error);
        records.value = [];
        total.value = 0;
      } finally {
        loading.value = false;
      }
    };

    const handleSizeChange = (val) => {
      pageSize.value = val;
      fetchRecords();
    };

    const handleCurrentChange = (val) => {
      currentPage.value = val;
      fetchRecords();
    };

    onMounted(() => {
      fetchRecords();
    });

    return {
      records,
      loading,
      currentPage,
      pageSize,
      total,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.operation-records-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
