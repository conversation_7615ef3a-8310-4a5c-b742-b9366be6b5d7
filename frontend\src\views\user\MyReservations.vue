<template>
  <div class="my-reservations">
    <div class="page-header">
      <h2>我的预约</h2>
      <div class="header-actions">
        <el-button type="primary" @click="refreshReservations">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div class="filter-section">
      <el-card shadow="never">
        <div class="filter-container">
          <div class="filter-item">
            <span class="filter-label">状态：</span>
            <el-select
              v-model="filters.status"
              placeholder="全部状态"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">日期：</span>
            <el-date-picker
              v-model="filters.date"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleFilterChange"
            />
          </div>

          <div class="filter-item">
            <span class="filter-label">排序：</span>
            <el-select
              v-model="sortBy"
              placeholder="排序方式"
              @change="handleSortChange"
            >
              <el-option
                v-for="option in sortOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated style="margin-top: 20px" />
    </div>

    <div v-else-if="filteredReservations.length === 0" class="empty-container">
      <el-empty description="没有找到符合条件的预约记录" />
    </div>

    <div v-else class="reservation-list">
      <el-card
        v-for="reservation in filteredReservations"
        :key="reservation.id"
        class="reservation-card"
        :class="{ 'reservation-active': isActiveReservation(reservation) }"
      >
        <div class="reservation-header">
          <div class="reservation-status">
            <el-tag :type="getReservationStatusType(reservation.status)">
              {{ getReservationStatusText(reservation.status) }}
            </el-tag>
            <span class="reservation-id">预约号: {{ reservation.id }}</span>
          </div>
          <div class="reservation-time">
            {{ formatDate(reservation.created_at) }}
          </div>
        </div>

        <div class="reservation-content">
          <div class="seat-info">
            <h3>{{ reservation.seat.room.name }}</h3>
            <p class="seat-location">{{ reservation.seat.room.location }}</p>
            <p class="seat-number">
              座位号: {{ reservation.seat.seat_number }}
              <el-tag
                v-if="reservation.seat.is_power_outlet"
                size="small"
                effect="plain"
              >
                有电源
              </el-tag>
              <el-tag
                v-if="reservation.seat.is_window_seat"
                size="small"
                effect="plain"
              >
                靠窗
              </el-tag>
            </p>
          </div>

          <div class="time-info">
            <p class="time-label">预约时间</p>
            <p class="time-value">
              {{ formatDateTime(reservation.start_time) }} -
              {{ formatDateTime(reservation.end_time) }}
            </p>
            <p class="time-duration">
              {{
                calculateDuration(reservation.start_time, reservation.end_time)
              }}
            </p>
          </div>
        </div>

        <div class="reservation-footer">
          <template v-if="reservation.status === 'pending'">
            <el-button type="primary" @click="showCheckInDialog(reservation)"
              >签到</el-button
            >
            <el-button type="danger" @click="showCancelDialog(reservation)"
              >取消预约</el-button
            >
          </template>

          <template v-else-if="reservation.status === 'checked_in'">
            <el-button type="success" @click="showCheckOutDialog(reservation)"
              >签退</el-button
            >
          </template>

          <template v-else>
            <el-button type="info" @click="showReservationDetail(reservation)"
              >查看详情</el-button
            >
          </template>
        </div>
      </el-card>
    </div>

    <!-- 签到对话框 -->
    <el-dialog v-model="checkInDialogVisible" title="座位签到" width="400px">
      <div v-if="selectedReservation" class="check-in-dialog">
        <div class="qr-code-container">
          <div class="qr-code" ref="qrCodeRef"></div>
        </div>

        <div class="check-in-info">
          <p>预约号: {{ selectedReservation.id }}</p>
          <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>
          <p>签到码: {{ selectedReservation.reservation_code }}</p>
          <p class="check-in-tip">请使用自习室终端扫描二维码完成签到</p>
        </div>

        <div class="manual-check-in">
          <el-divider>或手动签到</el-divider>
          <el-button type="primary" @click="handleCheckIn">手动签到</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 签退对话框 -->
    <el-dialog v-model="checkOutDialogVisible" title="座位签退" width="400px">
      <div v-if="selectedReservation" class="check-out-dialog">
        <p>您确定要签退座位吗？</p>
        <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>
        <p>
          预约时间: {{ formatDateTime(selectedReservation.start_time) }} -
          {{ formatDateTime(selectedReservation.end_time) }}
        </p>

        <div class="dialog-footer">
          <el-button @click="checkOutDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleCheckOut"
            :loading="processing"
          >
            确认签退
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 取消预约对话框 -->
    <el-dialog v-model="cancelDialogVisible" title="取消预约" width="400px">
      <div v-if="selectedReservation" class="cancel-dialog">
        <p>您确定要取消此预约吗？</p>
        <p>座位号: {{ selectedReservation.seat?.seat_number }}</p>
        <p>
          预约时间: {{ formatDateTime(selectedReservation.start_time) }} -
          {{ formatDateTime(selectedReservation.end_time) }}
        </p>

        <div class="cancel-warning">
          <el-alert
            title="取消预约提示"
            type="warning"
            description="距离预约开始时间不足30分钟取消，可能会影响您的信誉分。"
            show-icon
            :closable="false"
          />
        </div>

        <div class="dialog-footer">
          <el-button @click="cancelDialogVisible = false">返回</el-button>
          <el-button type="danger" @click="handleCancel" :loading="processing"
            >确认取消</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, reactive, nextTick } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import QRCode from "qrcodejs2";

export default {
  name: "MyReservations",
  components: {
    Refresh,
  },
  setup() {
    const store = useStore();
    const router = useRouter();

    const loading = ref(true);
    const processing = ref(false);
    const qrCodeRef = ref(null);

    const checkInDialogVisible = ref(false);
    const checkOutDialogVisible = ref(false);
    const cancelDialogVisible = ref(false);
    const selectedReservation = ref(null);

    const filters = reactive({
      status: "",
      date: "",
    });

    const sortBy = ref("start_time");

    // 状态选项
    const statusOptions = [
      { value: "pending", label: "待签到" },
      { value: "checked_in", label: "已签到" },
      { value: "completed", label: "已完成" },
      { value: "cancelled", label: "已取消" },
      { value: "timeout", label: "已超时" },
    ];

    // 排序选项
    const sortOptions = [
      { value: "start_time", label: "按开始时间排序" },
      { value: "created_at", label: "按创建时间排序" },
      { value: "status", label: "按状态排序" },
    ];

    // 获取预约列表
    const getReservations = async () => {
      try {
        loading.value = true;
        await store.dispatch("seat/getMyReservations");
      } catch (error) {
        console.error("获取预约列表失败:", error);
        ElMessage.error("获取预约列表失败");
      } finally {
        loading.value = false;
      }
    };

    // 刷新预约列表
    const refreshReservations = () => {
      getReservations();
    };

    // 过滤后的预约列表
    const filteredReservations = computed(() => {
      let result = store.getters["seat/myReservations"];

      // 状态过滤
      if (filters.status) {
        result = result.filter(
          (reservation) => reservation.status === filters.status
        );
      }

      // 日期过滤
      if (filters.date) {
        const filterDate = new Date(filters.date);
        filterDate.setHours(0, 0, 0, 0);

        const nextDay = new Date(filterDate);
        nextDay.setDate(nextDay.getDate() + 1);

        result = result.filter((reservation) => {
          const startTime = new Date(reservation.start_time);
          return startTime >= filterDate && startTime < nextDay;
        });
      }

      // 排序
      result = [...result].sort((a, b) => {
        switch (sortBy.value) {
          case "created_at":
            return new Date(b.created_at) - new Date(a.created_at);
          case "status":
            return getStatusPriority(a.status) - getStatusPriority(b.status);
          case "start_time":
          default:
            return new Date(a.start_time) - new Date(b.start_time);
        }
      });

      return result;
    });

    // 获取状态优先级（用于排序）
    const getStatusPriority = (status) => {
      switch (status) {
        case "checked_in":
          return 1;
        case "pending":
          return 2;
        case "completed":
          return 3;
        case "cancelled":
          return 4;
        case "timeout":
          return 5;
        default:
          return 6;
      }
    };

    // 处理过滤变化
    const handleFilterChange = () => {
      // 过滤逻辑已在计算属性中实现
    };

    // 处理排序变化
    const handleSortChange = () => {
      // 排序逻辑已在计算属性中实现
    };

    // 是否是活跃预约（待签到或已签到）
    const isActiveReservation = (reservation) => {
      return (
        reservation.status === "pending" || reservation.status === "checked_in"
      );
    };

    // 显示签到对话框
    const showCheckInDialog = (reservation) => {
      selectedReservation.value = reservation;
      checkInDialogVisible.value = true;

      // 生成二维码
      nextTick(() => {
        if (qrCodeRef.value) {
          // 清空容器
          qrCodeRef.value.innerHTML = "";

          // 生成二维码
          new QRCode(qrCodeRef.value, {
            text: reservation.reservation_code,
            width: 200,
            height: 200,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.H,
          });
        }
      });
    };

    // 显示签退对话框
    const showCheckOutDialog = (reservation) => {
      selectedReservation.value = reservation;
      checkOutDialogVisible.value = true;
    };

    // 显示取消对话框
    const showCancelDialog = (reservation) => {
      selectedReservation.value = reservation;
      cancelDialogVisible.value = true;
    };

    // 显示预约详情
    const showReservationDetail = async (reservation) => {
      try {
        // 获取预约详情
        await store.dispatch("seat/getReservationById", reservation.id);

        // 跳转到预约详情页面
        router.push({
          path: `/seat/reservation/${reservation.id}`,
          query: { mode: "view" },
        });
      } catch (error) {
        ElMessage.error("获取预约详情失败");
      }
    };

    // 处理签到
    const handleCheckIn = async () => {
      if (!selectedReservation.value) return;

      try {
        processing.value = true;

        await store.dispatch("seat/checkIn", {
          reservationId: selectedReservation.value.id,
        });

        ElMessage.success("签到成功");
        checkInDialogVisible.value = false;
        refreshReservations();
      } catch (error) {
        ElMessage.error(error.message || "签到失败");
      } finally {
        processing.value = false;
      }
    };

    // 处理签退
    const handleCheckOut = async () => {
      if (!selectedReservation.value) return;

      try {
        processing.value = true;

        await store.dispatch("seat/checkOut", {
          reservationId: selectedReservation.value.id,
        });

        ElMessage.success("签退成功");
        checkOutDialogVisible.value = false;
        refreshReservations();
      } catch (error) {
        ElMessage.error(error.message || "签退失败");
      } finally {
        processing.value = false;
      }
    };

    // 处理取消预约
    const handleCancel = async () => {
      if (!selectedReservation.value) return;

      try {
        processing.value = true;

        await store.dispatch("seat/cancelReservation", {
          reservationId: selectedReservation.value.id,
        });

        ElMessage.success("预约已取消");
        cancelDialogVisible.value = false;
        refreshReservations();
      } catch (error) {
        ElMessage.error(error.message || "取消预约失败");
      } finally {
        processing.value = false;
      }
    };

    // 获取预约状态类型
    const getReservationStatusType = (status) => {
      switch (status) {
        case "pending":
          return "warning";
        case "checked_in":
          return "success";
        case "completed":
          return "info";
        case "cancelled":
          return "danger";
        case "timeout":
          return "danger";
        default:
          return "info";
      }
    };

    // 获取预约状态文本
    const getReservationStatusText = (status) => {
      switch (status) {
        case "pending":
          return "待签到";
        case "checked_in":
          return "已签到";
        case "completed":
          return "已完成";
        case "cancelled":
          return "已取消";
        case "timeout":
          return "已超时";
        default:
          return "未知状态";
      }
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return "";

      const date = new Date(dateString);
      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(
        date.getDate()
      )}`;
    };

    // 格式化日期时间
    const formatDateTime = (dateTimeString) => {
      if (!dateTimeString) return "";

      const date = new Date(dateTimeString);
      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(
        date.getDate()
      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
    };

    // 计算时长
    const calculateDuration = (startTime, endTime) => {
      if (!startTime || !endTime) return "";

      const start = new Date(startTime);
      const end = new Date(endTime);
      const diffMs = end - start;
      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      return `${diffHrs}小时${diffMins}分钟`;
    };

    // 补零
    function padZero(num) {
      return num < 10 ? `0${num}` : num;
    }

    onMounted(() => {
      getReservations();
    });

    return {
      loading,
      processing,
      qrCodeRef,
      filters,
      sortBy,
      statusOptions,
      sortOptions,
      checkInDialogVisible,
      checkOutDialogVisible,
      cancelDialogVisible,
      selectedReservation,
      filteredReservations,
      refreshReservations,
      handleFilterChange,
      handleSortChange,
      isActiveReservation,
      showCheckInDialog,
      showCheckOutDialog,
      showCancelDialog,
      showReservationDetail,
      handleCheckIn,
      handleCheckOut,
      handleCancel,
      getReservationStatusType,
      getReservationStatusText,
      formatDate,
      formatDateTime,
      calculateDuration,
    };
  },
};
</script>

<style lang="scss" scoped>
.my-reservations {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }
}

.filter-section {
  margin-bottom: 20px;

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .filter-item {
      display: flex;
      align-items: center;

      .filter-label {
        margin-right: 10px;
        white-space: nowrap;
      }
    }
  }
}

.loading-container,
.empty-container {
  padding: 40px 0;
  text-align: center;
}

.reservation-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.reservation-card {
  transition: transform 0.3s, box-shadow 0.3s;

  &.reservation-active {
    border-left: 4px solid #409eff;

    &:hover {
      transform: translateX(5px);
    }
  }

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .reservation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .reservation-status {
      display: flex;
      align-items: center;
      gap: 10px;

      .reservation-id {
        color: #909399;
        font-size: 14px;
      }
    }

    .reservation-time {
      color: #909399;
      font-size: 14px;
    }
  }

  .reservation-content {
    display: flex;
    margin-bottom: 15px;

    .seat-info {
      flex: 1;

      h3 {
        margin: 0 0 5px 0;
      }

      .seat-location {
        margin: 0 0 5px 0;
        color: #606266;
      }

      .seat-number {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }

    .time-info {
      flex: 1;
      text-align: right;

      .time-label {
        margin: 0 0 5px 0;
        color: #909399;
      }

      .time-value {
        margin: 0 0 5px 0;
        font-weight: bold;
      }

      .time-duration {
        margin: 0;
        color: #606266;
      }
    }
  }

  .reservation-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

.check-in-dialog {
  .qr-code-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    .qr-code {
      padding: 10px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  .check-in-info {
    text-align: center;
    margin-bottom: 20px;

    p {
      margin: 5px 0;
    }

    .check-in-tip {
      color: #909399;
      font-size: 14px;
      margin-top: 10px;
    }
  }

  .manual-check-in {
    text-align: center;
    margin-top: 20px;
  }
}

.check-out-dialog,
.cancel-dialog {
  p {
    margin: 10px 0;
  }

  .cancel-warning {
    margin: 20px 0;
  }

  .dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
