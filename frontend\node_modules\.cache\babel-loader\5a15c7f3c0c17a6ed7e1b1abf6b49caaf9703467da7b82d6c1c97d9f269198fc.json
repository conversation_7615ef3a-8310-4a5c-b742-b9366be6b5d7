{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { createRouter, createWebHashHistory } from \"vue-router\";\nimport Layout from \"@/components/layout/Layout.vue\";\n\n// 路由配置\nconst routes = [{\n  path: \"/\",\n  redirect: () => {\n    // 根据登录状态决定重定向到哪里\n    const isLoggedIn = localStorage.getItem(\"token\") !== null;\n    return isLoggedIn ? \"/dashboard\" : \"/login\";\n  }\n}, {\n  path: \"/dashboard\",\n  component: Layout,\n  children: [{\n    path: \"\",\n    name: \"Dashboard\",\n    component: () => import(\"@/views/Dashboard.vue\"),\n    meta: {\n      title: \"首页\",\n      requiresAuth: true\n    }\n  }]\n}, {\n  path: \"/login\",\n  name: \"Login\",\n  component: () => import(\"@/views/auth/Login.vue\"),\n  meta: {\n    title: \"登录\"\n  }\n}, {\n  path: \"/register\",\n  name: \"Register\",\n  component: () => import(\"@/views/auth/Register.vue\"),\n  meta: {\n    title: \"注册\"\n  }\n}, {\n  path: \"/user\",\n  component: Layout,\n  redirect: \"/user/profile\",\n  meta: {\n    requiresAuth: true\n  },\n  children: [{\n    path: \"profile\",\n    name: \"UserProfile\",\n    component: () => import(\"@/views/user/UserProfile.vue\"),\n    meta: {\n      title: \"个人信息\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"reservations\",\n    name: \"MyReservations\",\n    component: () => import(\"@/views/user/MyReservations.vue\"),\n    meta: {\n      title: \"我的预约\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"records\",\n    name: \"OperationRecords\",\n    component: () => import(\"@/views/user/OperationRecords.vue\"),\n    meta: {\n      title: \"操作记录\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"credit\",\n    name: \"CreditRecords\",\n    component: () => import(\"@/views/user/CreditRecords.vue\"),\n    meta: {\n      title: \"信誉分记录\",\n      requiresAuth: true\n    }\n  }]\n}, {\n  path: \"/seat\",\n  component: Layout,\n  redirect: \"/seat/rooms\",\n  meta: {\n    requiresAuth: true\n  },\n  children: [{\n    path: \"rooms\",\n    name: \"RoomList\",\n    component: () => import(\"@/views/seat/RoomList.vue\"),\n    meta: {\n      title: \"自习室列表\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"map\",\n    name: \"SeatMap\",\n    component: () => import(\"@/views/seat/SeatMap.vue\"),\n    meta: {\n      title: \"座位地图\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"reservation\",\n    name: \"SeatReservation\",\n    component: () => import(\"@/views/seat/SeatReservation.vue\"),\n    meta: {\n      title: \"座位预约\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"reservation/:id\",\n    name: \"ReservationDetail\",\n    component: () => import(\"@/views/seat/ReservationDetail.vue\"),\n    meta: {\n      title: \"预约详情\",\n      requiresAuth: true\n    }\n  }, {\n    path: \"checkin\",\n    name: \"CheckInOut\",\n    component: () => import(\"@/views/seat/CheckInOut.vue\"),\n    meta: {\n      title: \"签到签退\",\n      requiresAuth: true\n    }\n  }]\n}, {\n  path: \"/help\",\n  component: Layout,\n  children: [{\n    path: \"\",\n    name: \"Help\",\n    component: () => import(\"@/views/Help.vue\"),\n    meta: {\n      title: \"帮助中心\"\n    }\n  }]\n}, {\n  path: \"/:pathMatch(.*)*\",\n  name: \"NotFound\",\n  component: () => import(\"@/views/NotFound.vue\"),\n  meta: {\n    title: \"页面不存在\"\n  }\n}];\nconst router = createRouter({\n  history: createWebHashHistory(),\n  routes\n});\n\n// 全局前置守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  document.title = to.meta.title ? `${to.meta.title} - 基于国密算法的图书馆自习室座位管理系统` : \"基于国密算法的图书馆自习室座位管理系统\";\n\n  // 检查用户登录状态\n  const isLoggedIn = localStorage.getItem(\"token\") !== null;\n\n  // 如果已登录用户访问登录或注册页面，重定向到首页\n  if (isLoggedIn && (to.path === \"/login\" || to.path === \"/register\")) {\n    next(\"/dashboard\");\n    return;\n  }\n\n  // 检查是否需要登录认证\n  if (to.matched.some(record => record.meta.requiresAuth)) {\n    if (!isLoggedIn) {\n      // 未登录，重定向到登录页\n      next({\n        path: \"/login\",\n        query: {\n          redirect: to.fullPath\n        }\n      });\n    } else {\n      // 已登录，允许访问\n      next();\n    }\n  } else {\n    // 不需要登录，允许访问\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHashHistory", "Layout", "routes", "path", "redirect", "isLoggedIn", "localStorage", "getItem", "component", "children", "name", "meta", "title", "requiresAuth", "router", "history", "beforeEach", "to", "from", "next", "document", "matched", "some", "record", "query", "fullPath"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHashHistory } from \"vue-router\";\nimport Layout from \"@/components/layout/Layout.vue\";\n\n// 路由配置\nconst routes = [\n  {\n    path: \"/\",\n    redirect: () => {\n      // 根据登录状态决定重定向到哪里\n      const isLoggedIn = localStorage.getItem(\"token\") !== null;\n      return isLoggedIn ? \"/dashboard\" : \"/login\";\n    },\n  },\n  {\n    path: \"/dashboard\",\n    component: Layout,\n    children: [\n      {\n        path: \"\",\n        name: \"Dashboard\",\n        component: () => import(\"@/views/Dashboard.vue\"),\n        meta: { title: \"首页\", requiresAuth: true },\n      },\n    ],\n  },\n  {\n    path: \"/login\",\n    name: \"Login\",\n    component: () => import(\"@/views/auth/Login.vue\"),\n    meta: { title: \"登录\" },\n  },\n  {\n    path: \"/register\",\n    name: \"Register\",\n    component: () => import(\"@/views/auth/Register.vue\"),\n    meta: { title: \"注册\" },\n  },\n  {\n    path: \"/user\",\n    component: Layout,\n    redirect: \"/user/profile\",\n    meta: { requiresAuth: true },\n    children: [\n      {\n        path: \"profile\",\n        name: \"UserProfile\",\n        component: () => import(\"@/views/user/UserProfile.vue\"),\n        meta: { title: \"个人信息\", requiresAuth: true },\n      },\n      {\n        path: \"reservations\",\n        name: \"MyReservations\",\n        component: () => import(\"@/views/user/MyReservations.vue\"),\n        meta: { title: \"我的预约\", requiresAuth: true },\n      },\n      {\n        path: \"records\",\n        name: \"OperationRecords\",\n        component: () => import(\"@/views/user/OperationRecords.vue\"),\n        meta: { title: \"操作记录\", requiresAuth: true },\n      },\n      {\n        path: \"credit\",\n        name: \"CreditRecords\",\n        component: () => import(\"@/views/user/CreditRecords.vue\"),\n        meta: { title: \"信誉分记录\", requiresAuth: true },\n      },\n    ],\n  },\n  {\n    path: \"/seat\",\n    component: Layout,\n    redirect: \"/seat/rooms\",\n    meta: { requiresAuth: true },\n    children: [\n      {\n        path: \"rooms\",\n        name: \"RoomList\",\n        component: () => import(\"@/views/seat/RoomList.vue\"),\n        meta: { title: \"自习室列表\", requiresAuth: true },\n      },\n      {\n        path: \"map\",\n        name: \"SeatMap\",\n        component: () => import(\"@/views/seat/SeatMap.vue\"),\n        meta: { title: \"座位地图\", requiresAuth: true },\n      },\n      {\n        path: \"reservation\",\n        name: \"SeatReservation\",\n        component: () => import(\"@/views/seat/SeatReservation.vue\"),\n        meta: { title: \"座位预约\", requiresAuth: true },\n      },\n      {\n        path: \"reservation/:id\",\n        name: \"ReservationDetail\",\n        component: () => import(\"@/views/seat/ReservationDetail.vue\"),\n        meta: { title: \"预约详情\", requiresAuth: true },\n      },\n      {\n        path: \"checkin\",\n        name: \"CheckInOut\",\n        component: () => import(\"@/views/seat/CheckInOut.vue\"),\n        meta: { title: \"签到签退\", requiresAuth: true },\n      },\n    ],\n  },\n\n  {\n    path: \"/help\",\n    component: Layout,\n    children: [\n      {\n        path: \"\",\n        name: \"Help\",\n        component: () => import(\"@/views/Help.vue\"),\n        meta: { title: \"帮助中心\" },\n      },\n    ],\n  },\n  {\n    path: \"/:pathMatch(.*)*\",\n    name: \"NotFound\",\n    component: () => import(\"@/views/NotFound.vue\"),\n    meta: { title: \"页面不存在\" },\n  },\n];\n\nconst router = createRouter({\n  history: createWebHashHistory(),\n  routes,\n});\n\n// 全局前置守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  document.title = to.meta.title\n    ? `${to.meta.title} - 基于国密算法的图书馆自习室座位管理系统`\n    : \"基于国密算法的图书馆自习室座位管理系统\";\n\n  // 检查用户登录状态\n  const isLoggedIn = localStorage.getItem(\"token\") !== null;\n\n  // 如果已登录用户访问登录或注册页面，重定向到首页\n  if (isLoggedIn && (to.path === \"/login\" || to.path === \"/register\")) {\n    next(\"/dashboard\");\n    return;\n  }\n\n  // 检查是否需要登录认证\n  if (to.matched.some((record) => record.meta.requiresAuth)) {\n    if (!isLoggedIn) {\n      // 未登录，重定向到登录页\n      next({\n        path: \"/login\",\n        query: { redirect: to.fullPath },\n      });\n    } else {\n      // 已登录，允许访问\n      next();\n    }\n  } else {\n    // 不需要登录，允许访问\n    next();\n  }\n});\n\nexport default router;\n"], "mappings": ";;AAAA,SAASA,YAAY,EAAEC,oBAAoB,QAAQ,YAAY;AAC/D,OAAOC,MAAM,MAAM,gCAAgC;;AAEnD;AACA,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAEA,CAAA,KAAM;IACd;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI;IACzD,OAAOF,UAAU,GAAG,YAAY,GAAG,QAAQ;EAC7C;AACF,CAAC,EACD;EACEF,IAAI,EAAE,YAAY;EAClBK,SAAS,EAAEP,MAAM;EACjBQ,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,EAAE;IACRO,IAAI,EAAE,WAAW;IACjBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IAChDG,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAK;EAC1C,CAAC;AAEL,CAAC,EACD;EACEV,IAAI,EAAE,QAAQ;EACdO,IAAI,EAAE,OAAO;EACbF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;EACjDG,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAK;AACtB,CAAC,EACD;EACET,IAAI,EAAE,WAAW;EACjBO,IAAI,EAAE,UAAU;EAChBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;EACpDG,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAK;AACtB,CAAC,EACD;EACET,IAAI,EAAE,OAAO;EACbK,SAAS,EAAEP,MAAM;EACjBG,QAAQ,EAAE,eAAe;EACzBO,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK,CAAC;EAC5BJ,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,SAAS;IACfO,IAAI,EAAE,aAAa;IACnBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEV,IAAI,EAAE,cAAc;IACpBO,IAAI,EAAE,gBAAgB;IACtBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEV,IAAI,EAAE,SAAS;IACfO,IAAI,EAAE,kBAAkB;IACxBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC;IAC5DG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEV,IAAI,EAAE,QAAQ;IACdO,IAAI,EAAE,eAAe;IACrBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDG,IAAI,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,YAAY,EAAE;IAAK;EAC7C,CAAC;AAEL,CAAC,EACD;EACEV,IAAI,EAAE,OAAO;EACbK,SAAS,EAAEP,MAAM;EACjBG,QAAQ,EAAE,aAAa;EACvBO,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK,CAAC;EAC5BJ,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,OAAO;IACbO,IAAI,EAAE,UAAU;IAChBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDG,IAAI,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,YAAY,EAAE;IAAK;EAC7C,CAAC,EACD;IACEV,IAAI,EAAE,KAAK;IACXO,IAAI,EAAE,SAAS;IACfF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;IACnDG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEV,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE,iBAAiB;IACvBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEV,IAAI,EAAE,iBAAiB;IACvBO,IAAI,EAAE,mBAAmB;IACzBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEV,IAAI,EAAE,SAAS;IACfO,IAAI,EAAE,YAAY;IAClBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAK;EAC5C,CAAC;AAEL,CAAC,EAED;EACEV,IAAI,EAAE,OAAO;EACbK,SAAS,EAAEP,MAAM;EACjBQ,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,EAAE;IACRO,IAAI,EAAE,MAAM;IACZF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC;IAC3CG,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,kBAAkB;EACxBO,IAAI,EAAE,UAAU;EAChBF,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;EAC/CG,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAQ;AACzB,CAAC,CACF;AAED,MAAME,MAAM,GAAGf,YAAY,CAAC;EAC1BgB,OAAO,EAAEf,oBAAoB,CAAC,CAAC;EAC/BE;AACF,CAAC,CAAC;;AAEF;AACAY,MAAM,CAACE,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC;EACAC,QAAQ,CAACR,KAAK,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK,GAC1B,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK,wBAAwB,GACxC,qBAAqB;;EAEzB;EACA,MAAMP,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI;;EAEzD;EACA,IAAIF,UAAU,KAAKY,EAAE,CAACd,IAAI,KAAK,QAAQ,IAAIc,EAAE,CAACd,IAAI,KAAK,WAAW,CAAC,EAAE;IACnEgB,IAAI,CAAC,YAAY,CAAC;IAClB;EACF;;EAEA;EACA,IAAIF,EAAE,CAACI,OAAO,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACZ,IAAI,CAACE,YAAY,CAAC,EAAE;IACzD,IAAI,CAACR,UAAU,EAAE;MACf;MACAc,IAAI,CAAC;QACHhB,IAAI,EAAE,QAAQ;QACdqB,KAAK,EAAE;UAAEpB,QAAQ,EAAEa,EAAE,CAACQ;QAAS;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAN,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IACL;IACAA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}