<template>
  <div class="credit-records">
    <div class="page-header">
      <h2>信誉分记录</h2>
      <div class="header-actions">
        <el-button type="primary" @click="refreshRecords">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div class="filter-section">
      <el-card shadow="hover" class="filter-card">
        <div class="filter-container">
          <div class="filter-item">
            <span class="filter-label">类型：</span>
            <el-select
              v-model="filters.type"
              placeholder="记录类型"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="option in typeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">日期：</span>
            <el-date-picker
              v-model="filters.date"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleFilterChange"
            />
          </div>

          <div class="filter-item">
            <span class="filter-label">排序：</span>
            <el-select
              v-model="sortBy"
              placeholder="排序方式"
              @change="handleSortChange"
            >
              <el-option
                v-for="option in sortOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated style="margin-top: 20px" />
    </div>

    <div v-else-if="filteredRecords.length === 0" class="empty-container">
      <el-empty description="没有找到符合条件的信誉分记录" />
    </div>

    <div v-else class="records-list">
      <el-timeline>
        <el-timeline-item
          v-for="record in filteredRecords"
          :key="record.id"
          :type="getRecordType(record)"
          :color="getRecordColor(record)"
          :timestamp="formatDate(record.created_at)"
          placement="top"
        >
          <el-card shadow="hover" class="record-card">
            <div class="record-header">
              <div class="record-title">
                <span class="record-reason">{{ record.reason }}</span>
                <el-tag
                  :type="record.delta > 0 ? 'success' : 'danger'"
                  effect="dark"
                  size="small"
                >
                  {{ record.delta > 0 ? "+" : "" }}{{ record.delta }}分
                </el-tag>
              </div>
              <div class="record-score">信誉分: {{ record.score_after }}</div>
            </div>

            <div class="record-content">
              <p v-if="record.related_entity" class="record-related">
                相关实体: {{ getRelatedEntityText(record.related_entity) }}
                <span v-if="record.related_id"
                  >(ID: {{ record.related_id }})</span
                >
              </p>
              <p class="record-operator">
                操作类型: {{ getOperatorTypeText(record.operator_type) }}
              </p>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, reactive } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";

export default {
  name: "CreditRecords",
  components: {
    Refresh,
  },
  setup() {
    const store = useStore();
    const loading = ref(true);

    const filters = reactive({
      type: "", // positive, negative
      date: "",
    });

    const sortBy = ref("created_at");

    // 类型选项
    const typeOptions = [
      { value: "positive", label: "加分记录" },
      { value: "negative", label: "扣分记录" },
    ];

    // 排序选项
    const sortOptions = [
      { value: "created_at", label: "按时间排序" },
      { value: "delta", label: "按分值排序" },
      { value: "score_after", label: "按最终分数排序" },
    ];

    // 获取信誉分记录
    const getRecords = async () => {
      try {
        loading.value = true;
        await store.dispatch("user/getCreditRecords");
      } catch (error) {
        console.error("获取信誉分记录失败:", error);
        ElMessage.error("获取信誉分记录失败");
      } finally {
        loading.value = false;
      }
    };

    // 刷新记录
    const refreshRecords = () => {
      getRecords();
    };

    // 过滤后的记录
    const filteredRecords = computed(() => {
      let result = store.getters["user/creditRecords"];

      // 类型过滤
      if (filters.type) {
        if (filters.type === "positive") {
          result = result.filter((record) => record.delta > 0);
        } else if (filters.type === "negative") {
          result = result.filter((record) => record.delta < 0);
        }
      }

      // 日期过滤
      if (filters.date) {
        const filterDate = new Date(filters.date);
        filterDate.setHours(0, 0, 0, 0);

        const nextDay = new Date(filterDate);
        nextDay.setDate(nextDay.getDate() + 1);

        result = result.filter((record) => {
          const createdAt = new Date(record.created_at);
          return createdAt >= filterDate && createdAt < nextDay;
        });
      }

      // 排序
      result = [...result].sort((a, b) => {
        if (sortBy.value === "created_at") {
          return new Date(b.created_at) - new Date(a.created_at);
        } else if (sortBy.value === "delta") {
          return b.delta - a.delta;
        } else if (sortBy.value === "score_after") {
          return b.score_after - a.score_after;
        }
        return 0;
      });

      return result;
    });

    // 处理过滤变化
    const handleFilterChange = () => {
      // 过滤逻辑已在计算属性中实现
    };

    // 处理排序变化
    const handleSortChange = () => {
      // 排序逻辑已在计算属性中实现
    };

    // 获取记录类型
    const getRecordType = (record) => {
      return record.delta > 0 ? "success" : "danger";
    };

    // 获取记录颜色
    const getRecordColor = (record) => {
      return record.delta > 0 ? "#67c23a" : "#f56c6c";
    };

    // 获取相关实体文本
    const getRelatedEntityText = (entity) => {
      const entityMap = {
        reservation: "预约",
        check_in: "签到",
        check_out: "签退",
        violation: "违规",
      };
      return entityMap[entity] || entity;
    };

    // 获取操作员类型文本
    const getOperatorTypeText = (type) => {
      const typeMap = {
        system: "系统",
        admin: "管理员",
      };
      return typeMap[type] || type;
    };

    // 格式化日期
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    };

    onMounted(() => {
      getRecords();
    });

    return {
      loading,
      filters,
      sortBy,
      typeOptions,
      sortOptions,
      filteredRecords,
      refreshRecords,
      handleFilterChange,
      handleSortChange,
      getRecordType,
      getRecordColor,
      getRelatedEntityText,
      getOperatorTypeText,
      formatDate,
    };
  },
};
</script>

<style lang="scss" scoped>
.credit-records {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }
}

.filter-section {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  margin-right: 8px;
  white-space: nowrap;
}

.loading-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.empty-container {
  padding: 40px 0;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.records-list {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.record-card {
  margin-bottom: 10px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.record-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.record-reason {
  font-weight: bold;
}

.record-content {
  color: #606266;
  font-size: 14px;
}

.record-related,
.record-operator {
  margin: 5px 0;
}
</style>
