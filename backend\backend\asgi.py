"""
ASGI config for backend project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator
import apps.seat.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

# 获取Django ASGI应用
django_asgi_app = get_asgi_application()

# 配置ASGI应用
application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter(
                apps.seat.routing.websocket_urlpatterns
            )
        )
    ),
})
