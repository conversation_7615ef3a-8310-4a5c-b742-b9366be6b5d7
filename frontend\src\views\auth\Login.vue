<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h2>基于国密算法的图书馆自习室座位管理系统</h2>
      </div>

      <el-tabs v-model="activeTab" class="login-tabs">
        <el-tab-pane label="密码登录" name="password">
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-position="top"
            @keyup.enter="handlePasswordLogin"
          >
            <el-form-item label="学号" prop="studentId">
              <el-input
                v-model="passwordForm.studentId"
                placeholder="请输入学号"
                :prefix-icon="User"
              />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input
                v-model="passwordForm.password"
                type="password"
                placeholder="请输入密码"
                :prefix-icon="Lock"
                show-password
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                class="login-button"
                :loading="loading"
                @click="handlePasswordLogin"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="证书登录" name="certificate">
          <el-form
            ref="certificateFormRef"
            :model="certificateForm"
            :rules="certificateRules"
            label-position="top"
            @keyup.enter="handleCertificateLogin"
          >
            <el-form-item label="学号" prop="studentId">
              <el-input
                v-model="certificateForm.studentId"
                placeholder="请输入学号"
                :prefix-icon="User"
              />
            </el-form-item>

            <el-form-item label="私钥" prop="privateKey">
              <el-input
                v-model="certificateForm.privateKey"
                type="textarea"
                :rows="4"
                placeholder="请输入SM2私钥"
                show-password
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                class="login-button"
                :loading="loading"
                @click="handleCertificateLogin"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div class="login-footer">
        <p>
          还没有账号？
          <router-link to="/register">立即注册</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from "vue";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";
import { SM3Hasher, SM2Crypto } from "@/utils/crypto";

export default {
  name: "LoginView",
  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();

    const passwordFormRef = ref(null);
    const certificateFormRef = ref(null);
    const activeTab = ref("password");
    const loading = ref(false);

    // 密码登录表单
    const passwordForm = reactive({
      studentId: "",
      password: "",
    });

    // 证书登录表单
    const certificateForm = reactive({
      studentId: "",
      privateKey: "",
    });

    // 表单验证规则
    const passwordRules = {
      studentId: [
        { required: true, message: "请输入学号", trigger: "blur" },
        { pattern: /^\d{11}$/, message: "学号必须为11位数字", trigger: "blur" },
      ],
      password: [
        { required: true, message: "请输入密码", trigger: "blur" },
        { min: 6, max: 20, message: "密码长度应为6-20个字符", trigger: "blur" },
      ],
    };

    const certificateRules = {
      studentId: [
        { required: true, message: "请输入学号", trigger: "blur" },
        { min: 5, max: 20, message: "学号长度应为5-20个字符", trigger: "blur" },
      ],
      privateKey: [
        { required: true, message: "请输入SM2私钥", trigger: "blur" },
      ],
    };

    // 密码登录处理
    const handlePasswordLogin = async () => {
      if (!passwordFormRef.value) return;

      await passwordFormRef.value.validate(async (valid) => {
        if (!valid) return;

        try {
          loading.value = true;

          // 对密码进行SM3哈希
          const hashedPassword = SM3Hasher.hash(passwordForm.password);

          // 调用登录接口
          await store.dispatch("user/login", {
            studentId: passwordForm.studentId,
            password: hashedPassword,
          });

          ElMessage.success("登录成功");
          // 检查是否有重定向参数
          const redirect = route.query.redirect || "/dashboard";
          router.push(redirect);
        } catch (error) {
          ElMessage.error(error.message || "登录失败，请检查学号和密码");
        } finally {
          loading.value = false;
        }
      });
    };

    // 证书登录处理
    const handleCertificateLogin = async () => {
      if (!certificateFormRef.value) return;

      await certificateFormRef.value.validate(async (valid) => {
        if (!valid) return;

        try {
          loading.value = true;

          // 获取SM2挑战值
          const challengeResponse = await store.dispatch(
            "user/getSM2Challenge",
            {
              studentId: certificateForm.studentId,
            }
          );

          const { challenge } = challengeResponse;

          // 使用私钥对挑战值进行签名
          const signature = SM2Crypto.sign(
            certificateForm.privateKey,
            challenge
          );

          // 调用SM2登录接口
          await store.dispatch("user/sm2Login", {
            studentId: certificateForm.studentId,
            signature,
          });

          ElMessage.success("登录成功");
          // 检查是否有重定向参数
          const redirect = route.query.redirect || "/dashboard";
          router.push(redirect);
        } catch (error) {
          ElMessage.error(error.message || "证书登录失败，请检查学号和私钥");
        } finally {
          loading.value = false;
        }
      });
    };

    return {
      passwordFormRef,
      certificateFormRef,
      activeTab,
      loading,
      passwordForm,
      certificateForm,
      passwordRules,
      certificateRules,
      handlePasswordLogin,
      handleCertificateLogin,
      User,
      Lock,
    };
  },
};
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-image: url("@/assets/background.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

/* 移除半透明白色遮罩层 */

.login-card {
  width: 400px;
  padding: 30px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    font-size: 22px;
    color: #303133;
    margin: 0;
    font-weight: 600;
    line-height: 1.4;
  }
}

.login-tabs {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
}

.login-footer {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  color: #606266;

  a {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
