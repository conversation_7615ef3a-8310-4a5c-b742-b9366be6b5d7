/**
 * 加密请求封装
 * 提供加密请求拦截器和响应解密功能
 */
import axios from "axios";
import CryptoUtils from "./cryptoUtils";

// 创建加密请求的axios实例
const encryptedAxios = axios.create({
  baseURL: "/api/v1",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest",
  },
});

/**
 * 请求拦截器 - 加密请求数据
 */
encryptedAxios.interceptors.request.use(
  async (config) => {
    try {
      // 检查是否需要加密
      if (config.encrypt !== false && config.data) {
        // 加密请求数据
        const encryptedPackage = await CryptoUtils.encryptRequest(config.data);

        // 替换原始数据为加密包
        config.data = encryptedPackage;

        // 添加加密标记头
        config.headers["X-Encrypted"] = "true";
      }

      // 添加时间戳和随机数，防止重放攻击
      config.headers["X-Timestamp"] = Date.now().toString();
      config.headers["X-Nonce"] = Math.random().toString(36).substring(2, 15);

      return config;
    } catch (error) {
      console.error("请求加密失败:", error);
      return Promise.reject(error);
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 响应拦截器 - 解密响应数据
 */
encryptedAxios.interceptors.response.use(
  async (response) => {
    try {
      // 检查响应是否加密
      if (response.headers["x-encrypted"] === "true" && response.data) {
        // 获取客户端私钥
        const privateKey = localStorage.getItem("sm2_private_key");
        if (!privateKey) {
          throw new Error("未找到客户端私钥，无法解密响应");
        }

        // 解密响应数据
        const decryptedData = CryptoUtils.decryptResponse(
          response.data,
          privateKey
        );

        // 替换响应数据为解密后的数据
        response.data = decryptedData;
      }

      return response;
    } catch (error) {
      console.error("响应解密失败:", error);
      return Promise.reject(error);
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 加密GET请求
 * @param {string} url - 请求URL
 * @param {Object} params - 请求参数
 * @param {Object} config - axios配置
 * @returns {Promise} - axios响应
 */
export const encryptedGet = (url, params = {}, config = {}) => {
  return encryptedAxios.get(url, { ...config, params });
};

/**
 * 加密POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} config - axios配置
 * @returns {Promise} - axios响应
 */
export const encryptedPost = (url, data = {}, config = {}) => {
  return encryptedAxios.post(url, data, config);
};

/**
 * 加密PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} config - axios配置
 * @returns {Promise} - axios响应
 */
export const encryptedPut = (url, data = {}, config = {}) => {
  return encryptedAxios.put(url, data, config);
};

/**
 * 加密DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} config - axios配置
 * @returns {Promise} - axios响应
 */
export const encryptedDelete = (url, config = {}) => {
  return encryptedAxios.delete(url, config);
};

export default {
  encryptedGet,
  encryptedPost,
  encryptedPut,
  encryptedDelete,
  encryptedAxios,
};
