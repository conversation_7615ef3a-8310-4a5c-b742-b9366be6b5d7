import axios from "axios";
import { ElMessage } from "element-plus";
import router from "@/router";

/**
 * HTTP请求工具
 * 提供统一的API请求接口，支持加密传输和错误处理
 */

// 创建axios实例
const http = axios.create({
  baseURL: process.env.VUE_APP_API_URL || "/api",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
http.interceptors.request.use(
  async (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem("token");

    // 如果有token，则添加到请求头
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    // 检查是否需要加密请求数据
    if (config.encrypt && config.data) {
      try {
        // 动态导入加密工具
        const { default: CryptoUtils } = await import("@/utils/cryptoUtils");

        // 加密请求数据
        const encryptedPackage = await CryptoUtils.encryptRequest(config.data);

        // 替换请求数据
        config.data = encryptedPackage;

        // 添加加密标记
        config.headers["X-Encrypted"] = "true";
      } catch (error) {
        console.error("请求数据加密失败:", error);
        // 加密失败时继续发送原始数据，确保系统可用性
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  async (response) => {
    // 检查响应是否加密
    if (response.headers["x-encrypted"] === "true" && response.data) {
      try {
        // 动态导入加密工具
        const { default: CryptoUtils } = await import("@/utils/cryptoUtils");

        // 获取客户端私钥
        const privateKey = localStorage.getItem("sm2_private_key");
        if (!privateKey) {
          console.warn("未找到客户端私钥，无法解密响应");
        } else {
          // 解密响应数据
          const decryptedData = CryptoUtils.decryptResponse(
            response.data,
            privateKey
          );
          response.data = decryptedData;
        }
      } catch (error) {
        console.error("响应解密失败:", error);
        // 解密失败时使用原始数据，确保系统可用性
      }
    }

    // 检查响应状态
    if (response.data && response.data.code !== undefined) {
      if (response.data.code === 0) {
        // 成功响应，返回数据部分
        return response.data.data || response.data;
      } else {
        // 业务错误
        const error = new Error(response.data.message || "请求失败");
        error.code = response.data.code;
        return Promise.reject(error);
      }
    }

    // 直接返回响应数据
    return response.data;
  },
  (error) => {
    if (error.response) {
      // 处理响应错误
      switch (error.response.status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem("token");
          localStorage.removeItem("userInfo");

          // 如果不是登录页，则跳转到登录页
          if (router.currentRoute.value.path !== "/login") {
            ElMessage.error("登录已过期，请重新登录");
            router.push("/login");
          }
          break;

        case 403:
          // 禁止访问
          ElMessage.error("没有权限访问该资源");
          break;

        case 404:
          // 资源不存在
          ElMessage.error("请求的资源不存在");
          break;

        case 500:
          // 服务器错误
          ElMessage.error("服务器错误，请稍后重试");
          break;

        default:
          // 其他错误
          if (error.response.data && error.response.data.message) {
            ElMessage.error(error.response.data.message);
          } else {
            ElMessage.error("请求失败，请稍后重试");
          }
      }
    } else if (error.request) {
      // 请求发送但没有收到响应
      ElMessage.error("网络错误，请检查网络连接");
    } else {
      // 请求配置错误
      ElMessage.error("请求配置错误");
    }

    return Promise.reject(error);
  }
);

// 导出请求方法
export default {
  // GET请求
  get(url, params = {}, config = {}) {
    return http.get(url, { params, ...config });
  },

  // POST请求
  post(url, data = {}, config = {}) {
    return http.post(url, data, config);
  },

  // PUT请求
  put(url, data = {}, config = {}) {
    return http.put(url, data, config);
  },

  // DELETE请求
  delete(url, config = {}) {
    return http.delete(url, config);
  },

  // 加密POST请求
  encryptedPost(url, data = {}, config = {}) {
    return http.post(url, data, { ...config, encrypt: true });
  },

  // 加密PUT请求
  encryptedPut(url, data = {}, config = {}) {
    return http.put(url, data, { ...config, encrypt: true });
  },
};
