{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter, useRoute } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { User, Lock } from \"@element-plus/icons-vue\";\nimport { SM3Hasher, SM2Crypto } from \"@/utils/crypto\";\nexport default {\n  name: \"LoginView\",\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n    const passwordFormRef = ref(null);\n    const certificateFormRef = ref(null);\n    const activeTab = ref(\"password\");\n    const loading = ref(false);\n\n    // 密码登录表单\n    const passwordForm = reactive({\n      studentId: \"\",\n      password: \"\"\n    });\n\n    // 证书登录表单\n    const certificateForm = reactive({\n      studentId: \"\",\n      privateKey: \"\"\n    });\n\n    // 表单验证规则\n    const passwordRules = {\n      studentId: [{\n        required: true,\n        message: \"请输入学号\",\n        trigger: \"blur\"\n      }, {\n        pattern: /^\\d{11}$/,\n        message: \"学号必须为11位数字\",\n        trigger: \"blur\"\n      }],\n      password: [{\n        required: true,\n        message: \"请输入密码\",\n        trigger: \"blur\"\n      }, {\n        min: 6,\n        max: 20,\n        message: \"密码长度应为6-20个字符\",\n        trigger: \"blur\"\n      }]\n    };\n    const certificateRules = {\n      studentId: [{\n        required: true,\n        message: \"请输入学号\",\n        trigger: \"blur\"\n      }, {\n        min: 5,\n        max: 20,\n        message: \"学号长度应为5-20个字符\",\n        trigger: \"blur\"\n      }],\n      privateKey: [{\n        required: true,\n        message: \"请输入SM2私钥\",\n        trigger: \"blur\"\n      }]\n    };\n\n    // 密码登录处理\n    const handlePasswordLogin = async () => {\n      if (!passwordFormRef.value) return;\n      await passwordFormRef.value.validate(async valid => {\n        if (!valid) return;\n        try {\n          loading.value = true;\n\n          // 对密码进行SM3哈希\n          const hashedPassword = SM3Hasher.hash(passwordForm.password);\n\n          // 调用登录接口\n          await store.dispatch(\"user/login\", {\n            studentId: passwordForm.studentId,\n            password: hashedPassword\n          });\n          ElMessage.success(\"登录成功\");\n          // 检查是否有重定向参数\n          const redirect = route.query.redirect || \"/dashboard\";\n          router.push(redirect);\n        } catch (error) {\n          ElMessage.error(error.message || \"登录失败，请检查学号和密码\");\n        } finally {\n          loading.value = false;\n        }\n      });\n    };\n\n    // 证书登录处理\n    const handleCertificateLogin = async () => {\n      if (!certificateFormRef.value) return;\n      await certificateFormRef.value.validate(async valid => {\n        if (!valid) return;\n        try {\n          loading.value = true;\n\n          // 获取SM2挑战值\n          const challengeResponse = await store.dispatch(\"user/getSM2Challenge\", {\n            studentId: certificateForm.studentId\n          });\n          const {\n            challenge\n          } = challengeResponse;\n\n          // 使用私钥对挑战值进行签名\n          const signature = SM2Crypto.sign(certificateForm.privateKey, challenge);\n\n          // 调用SM2登录接口\n          await store.dispatch(\"user/sm2Login\", {\n            studentId: certificateForm.studentId,\n            signature\n          });\n          ElMessage.success(\"登录成功\");\n          // 检查是否有重定向参数\n          const redirect = route.query.redirect || \"/dashboard\";\n          router.push(redirect);\n        } catch (error) {\n          ElMessage.error(error.message || \"证书登录失败，请检查学号和私钥\");\n        } finally {\n          loading.value = false;\n        }\n      });\n    };\n    return {\n      passwordFormRef,\n      certificateFormRef,\n      activeTab,\n      loading,\n      passwordForm,\n      certificateForm,\n      passwordRules,\n      certificateRules,\n      handlePasswordLogin,\n      handleCertificateLogin,\n      User,\n      Lock\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "useStore", "useRouter", "useRoute", "ElMessage", "User", "Lock", "SM3Hasher", "SM2Crypto", "name", "setup", "store", "router", "passwordFormRef", "certificateFormRef", "activeTab", "loading", "passwordForm", "studentId", "password", "certificateForm", "privateKey", "passwordRules", "required", "message", "trigger", "pattern", "min", "max", "certificateRules", "handlePasswordLogin", "value", "validate", "valid", "hashedPassword", "hash", "dispatch", "success", "redirect", "route", "query", "push", "error", "handleCertificateLogin", "challengeResponse", "challenge", "signature", "sign"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-card\">\n      <div class=\"login-header\">\n        <h2>基于国密算法的图书馆自习室座位管理系统</h2>\n      </div>\n\n      <el-tabs v-model=\"activeTab\" class=\"login-tabs\">\n        <el-tab-pane label=\"密码登录\" name=\"password\">\n          <el-form\n            ref=\"passwordFormRef\"\n            :model=\"passwordForm\"\n            :rules=\"passwordRules\"\n            label-position=\"top\"\n            @keyup.enter=\"handlePasswordLogin\"\n          >\n            <el-form-item label=\"学号\" prop=\"studentId\">\n              <el-input\n                v-model=\"passwordForm.studentId\"\n                placeholder=\"请输入学号\"\n                :prefix-icon=\"User\"\n              />\n            </el-form-item>\n\n            <el-form-item label=\"密码\" prop=\"password\">\n              <el-input\n                v-model=\"passwordForm.password\"\n                type=\"password\"\n                placeholder=\"请输入密码\"\n                :prefix-icon=\"Lock\"\n                show-password\n              />\n            </el-form-item>\n\n            <el-form-item>\n              <el-button\n                type=\"primary\"\n                class=\"login-button\"\n                :loading=\"loading\"\n                @click=\"handlePasswordLogin\"\n              >\n                登录\n              </el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"证书登录\" name=\"certificate\">\n          <el-form\n            ref=\"certificateFormRef\"\n            :model=\"certificateForm\"\n            :rules=\"certificateRules\"\n            label-position=\"top\"\n            @keyup.enter=\"handleCertificateLogin\"\n          >\n            <el-form-item label=\"学号\" prop=\"studentId\">\n              <el-input\n                v-model=\"certificateForm.studentId\"\n                placeholder=\"请输入学号\"\n                :prefix-icon=\"User\"\n              />\n            </el-form-item>\n\n            <el-form-item label=\"私钥\" prop=\"privateKey\">\n              <el-input\n                v-model=\"certificateForm.privateKey\"\n                type=\"textarea\"\n                :rows=\"4\"\n                placeholder=\"请输入SM2私钥\"\n                show-password\n              />\n            </el-form-item>\n\n            <el-form-item>\n              <el-button\n                type=\"primary\"\n                class=\"login-button\"\n                :loading=\"loading\"\n                @click=\"handleCertificateLogin\"\n              >\n                登录\n              </el-button>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div class=\"login-footer\">\n        <p>\n          还没有账号？\n          <router-link to=\"/register\">立即注册</router-link>\n        </p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { useRouter, useRoute } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport { User, Lock } from \"@element-plus/icons-vue\";\nimport { SM3Hasher, SM2Crypto } from \"@/utils/crypto\";\n\nexport default {\n  name: \"LoginView\",\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n\n    const passwordFormRef = ref(null);\n    const certificateFormRef = ref(null);\n    const activeTab = ref(\"password\");\n    const loading = ref(false);\n\n    // 密码登录表单\n    const passwordForm = reactive({\n      studentId: \"\",\n      password: \"\",\n    });\n\n    // 证书登录表单\n    const certificateForm = reactive({\n      studentId: \"\",\n      privateKey: \"\",\n    });\n\n    // 表单验证规则\n    const passwordRules = {\n      studentId: [\n        { required: true, message: \"请输入学号\", trigger: \"blur\" },\n        { pattern: /^\\d{11}$/, message: \"学号必须为11位数字\", trigger: \"blur\" },\n      ],\n      password: [\n        { required: true, message: \"请输入密码\", trigger: \"blur\" },\n        { min: 6, max: 20, message: \"密码长度应为6-20个字符\", trigger: \"blur\" },\n      ],\n    };\n\n    const certificateRules = {\n      studentId: [\n        { required: true, message: \"请输入学号\", trigger: \"blur\" },\n        { min: 5, max: 20, message: \"学号长度应为5-20个字符\", trigger: \"blur\" },\n      ],\n      privateKey: [\n        { required: true, message: \"请输入SM2私钥\", trigger: \"blur\" },\n      ],\n    };\n\n    // 密码登录处理\n    const handlePasswordLogin = async () => {\n      if (!passwordFormRef.value) return;\n\n      await passwordFormRef.value.validate(async (valid) => {\n        if (!valid) return;\n\n        try {\n          loading.value = true;\n\n          // 对密码进行SM3哈希\n          const hashedPassword = SM3Hasher.hash(passwordForm.password);\n\n          // 调用登录接口\n          await store.dispatch(\"user/login\", {\n            studentId: passwordForm.studentId,\n            password: hashedPassword,\n          });\n\n          ElMessage.success(\"登录成功\");\n          // 检查是否有重定向参数\n          const redirect = route.query.redirect || \"/dashboard\";\n          router.push(redirect);\n        } catch (error) {\n          ElMessage.error(error.message || \"登录失败，请检查学号和密码\");\n        } finally {\n          loading.value = false;\n        }\n      });\n    };\n\n    // 证书登录处理\n    const handleCertificateLogin = async () => {\n      if (!certificateFormRef.value) return;\n\n      await certificateFormRef.value.validate(async (valid) => {\n        if (!valid) return;\n\n        try {\n          loading.value = true;\n\n          // 获取SM2挑战值\n          const challengeResponse = await store.dispatch(\n            \"user/getSM2Challenge\",\n            {\n              studentId: certificateForm.studentId,\n            }\n          );\n\n          const { challenge } = challengeResponse;\n\n          // 使用私钥对挑战值进行签名\n          const signature = SM2Crypto.sign(\n            certificateForm.privateKey,\n            challenge\n          );\n\n          // 调用SM2登录接口\n          await store.dispatch(\"user/sm2Login\", {\n            studentId: certificateForm.studentId,\n            signature,\n          });\n\n          ElMessage.success(\"登录成功\");\n          // 检查是否有重定向参数\n          const redirect = route.query.redirect || \"/dashboard\";\n          router.push(redirect);\n        } catch (error) {\n          ElMessage.error(error.message || \"证书登录失败，请检查学号和私钥\");\n        } finally {\n          loading.value = false;\n        }\n      });\n    };\n\n    return {\n      passwordFormRef,\n      certificateFormRef,\n      activeTab,\n      loading,\n      passwordForm,\n      certificateForm,\n      passwordRules,\n      certificateRules,\n      handlePasswordLogin,\n      handleCertificateLogin,\n      User,\n      Lock,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background-image: url(\"@/assets/background.jpg\");\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  position: relative;\n}\n\n/* 移除半透明白色遮罩层 */\n\n.login-card {\n  width: 400px;\n  padding: 30px;\n  background-color: rgba(255, 255, 255, 0.9);\n  border-radius: 8px;\n  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.2);\n  position: relative;\n  z-index: 1;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n\n  h2 {\n    font-size: 22px;\n    color: #303133;\n    margin: 0;\n    font-weight: 600;\n    line-height: 1.4;\n  }\n}\n\n.login-tabs {\n  margin-bottom: 20px;\n}\n\n.login-button {\n  width: 100%;\n}\n\n.login-footer {\n  margin-top: 20px;\n  text-align: center;\n  font-size: 14px;\n  color: #606266;\n\n  a {\n    color: #409eff;\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n</style>\n"], "mappings": ";AAkGA,SAASA,GAAG,EAAEC,QAAO,QAAS,KAAK;AACnC,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,SAAS,EAAEC,QAAO,QAAS,YAAY;AAChD,SAASC,SAAQ,QAAS,cAAc;AACxC,SAASC,IAAI,EAAEC,IAAG,QAAS,yBAAyB;AACpD,SAASC,SAAS,EAAEC,SAAQ,QAAS,gBAAgB;AAErD,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIV,QAAQ,CAAC,CAAC;IACxB,MAAMW,MAAK,GAAIV,SAAS,CAAC,CAAC;IAE1B,MAAMW,eAAc,GAAId,GAAG,CAAC,IAAI,CAAC;IACjC,MAAMe,kBAAiB,GAAIf,GAAG,CAAC,IAAI,CAAC;IACpC,MAAMgB,SAAQ,GAAIhB,GAAG,CAAC,UAAU,CAAC;IACjC,MAAMiB,OAAM,GAAIjB,GAAG,CAAC,KAAK,CAAC;;IAE1B;IACA,MAAMkB,YAAW,GAAIjB,QAAQ,CAAC;MAC5BkB,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMC,eAAc,GAAIpB,QAAQ,CAAC;MAC/BkB,SAAS,EAAE,EAAE;MACbG,UAAU,EAAE;IACd,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAY,GAAI;MACpBJ,SAAS,EAAE,CACT;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,OAAO,EAAE,UAAU;QAAEF,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACDN,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEE,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEJ,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,CAAC;IAElE,CAAC;IAED,MAAMI,gBAAe,GAAI;MACvBX,SAAS,EAAE,CACT;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEE,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEJ,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,CAAC,CAC/D;MACDJ,UAAU,EAAE,CACV;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAO,CAAC;IAE5D,CAAC;;IAED;IACA,MAAMK,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAI,CAACjB,eAAe,CAACkB,KAAK,EAAE;MAE5B,MAAMlB,eAAe,CAACkB,KAAK,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACpD,IAAI,CAACA,KAAK,EAAE;QAEZ,IAAI;UACFjB,OAAO,CAACe,KAAI,GAAI,IAAI;;UAEpB;UACA,MAAMG,cAAa,GAAI3B,SAAS,CAAC4B,IAAI,CAAClB,YAAY,CAACE,QAAQ,CAAC;;UAE5D;UACA,MAAMR,KAAK,CAACyB,QAAQ,CAAC,YAAY,EAAE;YACjClB,SAAS,EAAED,YAAY,CAACC,SAAS;YACjCC,QAAQ,EAAEe;UACZ,CAAC,CAAC;UAEF9B,SAAS,CAACiC,OAAO,CAAC,MAAM,CAAC;UACzB;UACA,MAAMC,QAAO,GAAIC,KAAK,CAACC,KAAK,CAACF,QAAO,IAAK,YAAY;UACrD1B,MAAM,CAAC6B,IAAI,CAACH,QAAQ,CAAC;QACvB,EAAE,OAAOI,KAAK,EAAE;UACdtC,SAAS,CAACsC,KAAK,CAACA,KAAK,CAAClB,OAAM,IAAK,eAAe,CAAC;QACnD,UAAU;UACRR,OAAO,CAACe,KAAI,GAAI,KAAK;QACvB;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMY,sBAAqB,GAAI,MAAAA,CAAA,KAAY;MACzC,IAAI,CAAC7B,kBAAkB,CAACiB,KAAK,EAAE;MAE/B,MAAMjB,kBAAkB,CAACiB,KAAK,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACvD,IAAI,CAACA,KAAK,EAAE;QAEZ,IAAI;UACFjB,OAAO,CAACe,KAAI,GAAI,IAAI;;UAEpB;UACA,MAAMa,iBAAgB,GAAI,MAAMjC,KAAK,CAACyB,QAAQ,CAC5C,sBAAsB,EACtB;YACElB,SAAS,EAAEE,eAAe,CAACF;UAC7B,CACF,CAAC;UAED,MAAM;YAAE2B;UAAU,IAAID,iBAAiB;;UAEvC;UACA,MAAME,SAAQ,GAAItC,SAAS,CAACuC,IAAI,CAC9B3B,eAAe,CAACC,UAAU,EAC1BwB,SACF,CAAC;;UAED;UACA,MAAMlC,KAAK,CAACyB,QAAQ,CAAC,eAAe,EAAE;YACpClB,SAAS,EAAEE,eAAe,CAACF,SAAS;YACpC4B;UACF,CAAC,CAAC;UAEF1C,SAAS,CAACiC,OAAO,CAAC,MAAM,CAAC;UACzB;UACA,MAAMC,QAAO,GAAIC,KAAK,CAACC,KAAK,CAACF,QAAO,IAAK,YAAY;UACrD1B,MAAM,CAAC6B,IAAI,CAACH,QAAQ,CAAC;QACvB,EAAE,OAAOI,KAAK,EAAE;UACdtC,SAAS,CAACsC,KAAK,CAACA,KAAK,CAAClB,OAAM,IAAK,iBAAiB,CAAC;QACrD,UAAU;UACRR,OAAO,CAACe,KAAI,GAAI,KAAK;QACvB;MACF,CAAC,CAAC;IACJ,CAAC;IAED,OAAO;MACLlB,eAAe;MACfC,kBAAkB;MAClBC,SAAS;MACTC,OAAO;MACPC,YAAY;MACZG,eAAe;MACfE,aAAa;MACbO,gBAAgB;MAChBC,mBAAmB;MACnBa,sBAAsB;MACtBtC,IAAI;MACJC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}