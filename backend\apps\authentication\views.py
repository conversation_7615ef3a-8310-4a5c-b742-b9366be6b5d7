"""
用户认证视图
"""
import logging
import binascii
import secrets
from django.shortcuts import render
from django.utils import timezone
from django.conf import settings
from rest_framework import status, viewsets, mixins, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from .models import User, UserSession, CreditRecord
from .serializers import (
    UserRegisterSerializer, UserLoginSerializer, UserSerializer,
    UserDetailSerializer, UserUpdateSerializer, CreditRecordSerializer,
    PasswordResetRequestSerializer, PasswordResetConfirmSerializer,
    SM2ChallengeSerializer, SM2LoginSerializer
)

from utils.crypto import SM3Hasher, SM4Crypto, SM2Crypto
from utils.crypto_utils import CryptoUtils

logger = logging.getLogger(__name__)


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

class RegisterView(APIView):
    """用户注册视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = UserRegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'message': '注册成功',
                'user_id': user.id
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoginView(APIView):
    """用户登录视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']

            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)

            # 记录会话信息
            user_session = UserSession.objects.create(
                user=user,
                token=access_token,
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                expires_at=timezone.now() + timezone.timedelta(hours=1)
            )

            # 更新用户最后登录时间
            user.last_login = timezone.now()
            user.save()

            return Response({
                'access': access_token,
                'refresh': refresh_token,
                'user_id': user.id,
                'student_id_hash': user.student_id_hash,
                'credit_score': user.credit_score
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class LogoutView(APIView):
    """用户登出视图"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        # 获取当前用户的会话
        try:
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                user_session = UserSession.objects.get(user=request.user, token=token, is_active=True)
                user_session.is_active = False
                user_session.save()
                return Response({'message': '登出成功'})
            return Response({'error': '无效的认证头'}, status=status.HTTP_400_BAD_REQUEST)
        except UserSession.DoesNotExist:
            return Response({'error': '会话不存在'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"登出失败: {str(e)}")
            return Response({'error': '登出失败'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserViewSet(viewsets.GenericViewSet,
                  mixins.RetrieveModelMixin,
                  mixins.UpdateModelMixin):
    """用户视图集"""
    queryset = User.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return UserDetailSerializer
        elif self.action == 'update' or self.action == 'partial_update':
            return UserUpdateSerializer
        elif self.action == 'reset_password_request':
            return PasswordResetRequestSerializer
        elif self.action == 'reset_password_confirm':
            return PasswordResetConfirmSerializer
        return UserSerializer

    def get_object(self):
        if self.kwargs.get('pk') == 'me':
            return self.request.user
        return super().get_object()

    @action(detail=False, methods=['get'])
    def me(self, request):
        """获取当前用户信息"""
        serializer = UserDetailSerializer(request.user)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def credit_records(self, request):
        """获取当前用户的信誉分记录"""
        records = CreditRecord.objects.filter(user=request.user).order_by('-created_at')
        page = self.paginate_queryset(records)
        if page is not None:
            serializer = CreditRecordSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = CreditRecordSerializer(records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], permission_classes=[permissions.AllowAny])
    def reset_password_request(self, request):
        """请求重置密码"""
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']

            # 生成重置令牌
            token = secrets.token_hex(32)

            # 获取令牌有效期配置
            from .models import SystemConfig
            expiry_seconds = int(SystemConfig.get_config('password_reset_token_expiry', 3600))
            expires_at = timezone.now() + timezone.timedelta(seconds=expiry_seconds)

            # 获取客户端IP地址
            client_ip = get_client_ip(request)

            # 创建密码重置令牌记录
            from .models import PasswordResetToken
            reset_token = PasswordResetToken.objects.create(
                user=user,
                token=token,
                expires_at=expires_at,
                ip_address=client_ip
            )

            # 发送重置邮件
            try:
                self._send_reset_email(user, token)
            except Exception as e:
                logger.error(f"发送重置邮件失败: {str(e)}")
                # 即使邮件发送失败，也不影响令牌创建

            return Response({
                'message': '密码重置邮件已发送，请查收邮件并按照指示操作',
                'expires_in': expiry_seconds
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[permissions.AllowAny])
    def reset_password_confirm(self, request):
        """确认重置密码"""
        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            token = serializer.validated_data['token']
            new_password = serializer.validated_data['new_password']

            # 查找对应的重置令牌
            from .models import PasswordResetToken
            try:
                reset_token = PasswordResetToken.objects.get(token=token)

                # 检查令牌是否有效
                if not reset_token.is_valid():
                    return Response({'error': '重置令牌已过期或已使用'}, status=status.HTTP_400_BAD_REQUEST)

                # 重置用户密码
                user = reset_token.user
                user.set_password(new_password)
                user.save()

                # 标记令牌为已使用
                reset_token.mark_as_used()

                # 记录密码重置操作
                logger.info(f"用户 {user.student_id_hash} 成功重置密码")

                return Response({'message': '密码重置成功'})

            except PasswordResetToken.DoesNotExist:
                return Response({'error': '无效的重置令牌'}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _send_reset_email(self, user, token):
        """发送密码重置邮件"""
        from django.core.mail import send_mail
        from django.conf import settings
        from .models import SystemConfig

        # 获取邮件配置
        email_config = SystemConfig.get_email_config()

        # 解密用户邮箱
        sm4_key = SystemConfig.get_sm4_key()
        try:
            encrypted_email = binascii.hexlify(user.email).decode()
            user_email = SM4Crypto.decrypt(sm4_key, encrypted_email)
        except Exception:
            logger.error(f"解密用户邮箱失败: {user.student_id_hash}")
            return

        # 构建重置链接
        reset_url = f"{settings.FRONTEND_URL}/reset-password?token={token}"

        # 邮件内容
        subject = '密码重置请求 - 图书馆自习室管理系统'
        message = f"""
您好，

您请求重置图书馆自习室管理系统的密码。

请点击以下链接重置您的密码：
{reset_url}

此链接将在1小时后过期。

如果您没有请求重置密码，请忽略此邮件。

图书馆自习室管理系统
        """

        try:
            send_mail(
                subject,
                message,
                email_config.get('from_email', '<EMAIL>'),
                [user_email],
                fail_silently=False,
            )
            logger.info(f"密码重置邮件已发送至: {user_email}")
        except Exception as e:
            logger.error(f"发送密码重置邮件失败: {str(e)}")
            raise


class CreditRecordViewSet(viewsets.ReadOnlyModelViewSet):
    """信誉分记录视图集"""
    serializer_class = CreditRecordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return CreditRecord.objects.filter(user=self.request.user).order_by('-created_at')


class SM2ChallengeView(APIView):
    """SM2挑战生成视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = SM2ChallengeSerializer(data=request.data)
        if serializer.is_valid():
            student_id = serializer.validated_data['student_id']

            # 计算学号的SM3哈希值用于查询
            student_id_hash = SM3Hasher.hash(student_id)

            try:
                # 查询用户
                user = User.objects.get(student_id_hash=student_id_hash)

                # 检查用户状态
                if user.status != 'active':
                    return Response({'error': f'账号状态异常: {user.status}'}, status=status.HTTP_400_BAD_REQUEST)

                # 检查用户是否有公钥
                if not user.public_key:
                    return Response({'error': '用户未注册SM2公钥'}, status=status.HTTP_400_BAD_REQUEST)

                # 检查公钥是否过期
                if user.public_key_expires and user.public_key_expires < timezone.now():
                    return Response({'error': 'SM2公钥已过期'}, status=status.HTTP_400_BAD_REQUEST)

                # 生成随机挑战值
                challenge = secrets.token_hex(16)

                # 将挑战值存储在会话中
                request.session['sm2_challenge'] = challenge
                request.session['sm2_student_id'] = student_id

                return Response({
                    'challenge': challenge,
                    'expires_in': 300  # 5分钟有效期
                })
            except User.DoesNotExist:
                return Response({'error': '用户不存在'}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                logger.error(f"生成SM2挑战失败: {str(e)}")
                return Response({'error': '生成挑战失败'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SM2LoginView(APIView):
    """SM2证书登录视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = SM2LoginSerializer(data=request.data)
        if serializer.is_valid():
            student_id = serializer.validated_data['student_id']
            signature = serializer.validated_data['signature']

            # 从会话中获取挑战值
            challenge = request.session.get('sm2_challenge')
            session_student_id = request.session.get('sm2_student_id')

            if not challenge or not session_student_id or session_student_id != student_id:
                return Response({'error': '无效的挑战值或会话已过期'}, status=status.HTTP_400_BAD_REQUEST)

            # 计算学号的SM3哈希值用于查询
            student_id_hash = SM3Hasher.hash(student_id)

            try:
                # 查询用户
                user = User.objects.get(student_id_hash=student_id_hash)

                # 检查用户状态
                if user.status != 'active':
                    return Response({'error': f'账号状态异常: {user.status}'}, status=status.HTTP_400_BAD_REQUEST)

                # 验证签名
                if not SM2Crypto.verify(user.public_key, challenge, signature):
                    # 增加登录失败次数
                    user.login_attempts += 1
                    if user.login_attempts >= 5:
                        user.status = 'locked'
                    user.save()
                    return Response({'error': '签名验证失败'}, status=status.HTTP_400_BAD_REQUEST)

                # 重置登录失败次数
                if user.login_attempts > 0:
                    user.login_attempts = 0
                    user.save()

                # 清除会话中的挑战值
                if 'sm2_challenge' in request.session:
                    del request.session['sm2_challenge']
                if 'sm2_student_id' in request.session:
                    del request.session['sm2_student_id']

                # 生成JWT令牌
                refresh = RefreshToken.for_user(user)
                access_token = str(refresh.access_token)
                refresh_token = str(refresh)

                # 记录会话信息
                user_session = UserSession.objects.create(
                    user=user,
                    token=access_token,
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    expires_at=timezone.now() + timezone.timedelta(hours=1)
                )

                # 更新用户最后登录时间
                user.last_login = timezone.now()
                user.save()

                return Response({
                    'access': access_token,
                    'refresh': refresh_token,
                    'user_id': user.id,
                    'student_id_hash': user.student_id_hash,
                    'credit_score': user.credit_score
                })
            except User.DoesNotExist:
                return Response({'error': '用户不存在'}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                logger.error(f"SM2登录失败: {str(e)}")
                return Response({'error': 'SM2登录失败'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
