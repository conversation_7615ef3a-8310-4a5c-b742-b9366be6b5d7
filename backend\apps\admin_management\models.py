from django.db import models
from django.utils import timezone
from utils.crypto import SM3Hasher

class Admin(models.Model):
    """管理员表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    username = models.CharField(max_length=50, unique=True, verbose_name='用户名')
    password = models.CharField(max_length=64, verbose_name='SM3哈希的密码')
    salt = models.CharField(max_length=32, null=True, blank=True, verbose_name='密码盐值')
    iterations = models.IntegerField(default=10000, verbose_name='密码哈希迭代次数')
    role = models.CharField(max_length=20, default='admin', verbose_name='角色(admin/super_admin)')
    auth_key = models.CharField(max_length=64, null=True, blank=True, verbose_name='不再使用')
    email = models.BinaryField(max_length=128, null=True, blank=True, verbose_name='SM4加密的邮箱')
    phone = models.BinaryField(max_length=64, null=True, blank=True, verbose_name='SM4加密的手机号')
    status = models.CharField(max_length=20, default='active', verbose_name='状态(active/disabled)')
    last_login = models.DateTimeField(null=True, blank=True, verbose_name='最后登录时间')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    # Django admin兼容字段
    is_staff = True 
    is_superuser = False  
    is_active = True  

    @property
    def user_permissions(self):
        """
        返回空集合，因为我们使用自定义的权限检查
        """
        from django.contrib.auth.models import Permission
        return Permission.objects.none()

    @property
    def groups(self):
        """
        返回空集合，因为我们不使用Django的组
        """
        from django.contrib.auth.models import Group
        return Group.objects.none()

    class Meta:
        db_table = 'admin'
        verbose_name = '管理员'
        verbose_name_plural = '管理员'

    def __str__(self):
        return f"Admin {self.username}"

    @property
    def is_authenticated(self):
        """
        始终返回True，表示这是一个已认证的用户
        用于Django admin认证
        """
        return True

    @property
    def is_anonymous(self):
        """
        始终返回False，表示这不是匿名用户
        用于Django admin认证
        """
        return False

    def get_username(self):
        """
        返回用户名
        用于Django admin认证
        """
        return self.username

    def check_password(self, raw_password):
        """
        检查密码是否正确
        用于Django admin认证
        """
        if not self.password or not raw_password:
            return False

        return SM3Hasher.verify(raw_password, self.password, self.salt, self.iterations)

    def set_password(self, raw_password):
        """
        设置管理员密码
        用于Django admin认证
        """
        if not raw_password:
            self.password = None
            self.salt = None
            self.iterations = 10000
        else:
            password_hash_result = SM3Hasher.hash_with_salt(raw_password)
            self.password = password_hash_result['hash']
            self.salt = password_hash_result['salt']
            self.iterations = password_hash_result['iterations']
            # 确保auth_key字段为空，避免密码被错误存储
            self.auth_key = None

    def has_perm(self, perm, obj=None):
        """
        检查是否有指定权限
        用于Django admin认证
        """
        # 超级管理员拥有所有权限
        if self.role == 'super_admin':
            return True

        # 普通管理员对Admin模型的权限控制
        if perm == 'admin_management.add_admin' or perm == 'admin_management.delete_admin':
            # 普通管理员不能添加或删除管理员
            return False

        if perm == 'admin_management.change_admin' and obj is not None:
            # 普通管理员只能修改自己的信息
            return obj.id == self.id

        if perm == 'admin_management.view_admin' and obj is not None:
            # 普通管理员只能查看自己的信息
            return obj.id == self.id

        # 对于其他所有权限，普通管理员都有权限
        return True

    def has_module_perms(self, app_label):
        """
        检查是否有访问指定应用的权限
        用于Django admin认证
        """
        # 所有管理员都可以访问所有模块
        return True


class AdminSession(models.Model):
    """管理员会话表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    admin = models.ForeignKey(Admin, on_delete=models.CASCADE, verbose_name='管理员ID')
    token = models.CharField(max_length=64, verbose_name='JWT令牌ID')
    ip_address = models.CharField(max_length=45, verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    is_active = models.BooleanField(default=True, verbose_name='是否活跃')

    class Meta:
        db_table = 'admin_session'
        verbose_name = '管理员会话'
        verbose_name_plural = '管理员会话'
        indexes = [
            models.Index(fields=['admin']),
            models.Index(fields=['token']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"Admin Session {self.id} - Admin {self.admin_id}"


class SystemConfig(models.Model):
    """系统配置表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    key = models.CharField(max_length=50, unique=True, verbose_name='配置键')
    value = models.TextField(verbose_name='配置值')
    description = models.CharField(max_length=255, null=True, blank=True, verbose_name='配置描述')
    is_encrypted = models.BooleanField(default=False, verbose_name='是否加密')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    updated_by = models.ForeignKey(Admin, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='更新人')

    class Meta:
        db_table = 'system_config'
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'

    def __str__(self):
        return f"Config {self.key}"


class AdminOperationLog(models.Model):
    """管理员操作日志表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    admin = models.ForeignKey(Admin, on_delete=models.CASCADE, verbose_name='管理员ID')
    operation = models.CharField(max_length=50, verbose_name='操作类型')
    description = models.TextField(verbose_name='操作描述')
    ip_address = models.CharField(max_length=45, verbose_name='IP地址')
    target_type = models.CharField(max_length=20, null=True, blank=True, verbose_name='目标类型')
    target_id = models.BigIntegerField(null=True, blank=True, verbose_name='目标ID')
    status = models.CharField(max_length=20, default='success', verbose_name='状态(success/failed)')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')

    class Meta:
        db_table = 'admin_operation_log'
        verbose_name = '管理员操作日志'
        verbose_name_plural = '管理员操作日志'
        indexes = [
            models.Index(fields=['admin']),
            models.Index(fields=['operation']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Admin Log {self.id} - {self.operation}"
